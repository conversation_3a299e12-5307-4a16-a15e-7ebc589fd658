<?php

/**
 * Uygulama Konfigürasyon Dosyası
 * Bu dosya tüm uygulama ayarlarını tek yerden yönetmek için oluşturulmuştur.
 * Hassas bilgiler .env dosyasından okunur, gü<PERSON><PERSON> bilgiler frontend'e gönderilir.
 */

require_once __DIR__ . '/env_helper.php';

class AppConfig
{
    /**
     * Uygulama genel ayarları (.env dosyasından okunur)
     */
    public static function getAppSettings()
    {
        return [
            'app_name' => EnvHelper::get('APP_NAME', 'ZDunya E-Commerce'),
            'app_version' => EnvHelper::get('APP_VERSION', '1.0.0'),
            'environment' => EnvHelper::get('APP_ENV', 'production'),
            'debug_mode' => EnvHelper::get('APP_DEBUG', false),
            'timezone' => EnvHelper::get('APP_TIMEZONE', 'Europe/Istanbul'),
            'language' => EnvHelper::get('APP_LANGUAGE', 'tr'),
            'currency' => EnvHelper::get('APP_CURRENCY', 'TRY')
        ];
    }

    /**
     * UI/UX Konfigürasyonu (.env dosyasından okunur)
     */
    public static function getUIConfig()
    {
        return [
            "productCardId" => EnvHelper::get('UI_PRODUCT_CARD_ID', 2),
            "showcaseSectionId" => EnvHelper::get('UI_SHOWCASE_SECTION_ID', 0),
            "productCardColor" => EnvHelper::get('UI_PRODUCT_CARD_COLOR', '#ffffff'),
            "productCardSecondaryColor" => EnvHelper::get('UI_PRODUCT_CARD_SECONDARY_COLOR', '#f0f0f0'),
            "productCardBorderColor" => EnvHelper::get('UI_PRODUCT_CARD_BORDER_COLOR', '#dcdcdc'),
            "productCardShadowColor" => EnvHelper::get('UI_PRODUCT_CARD_SHADOW_COLOR', '#aaaaaa'),
            "headerid" => EnvHelper::get('UI_HEADER_ID', 2),
            "footerid" => EnvHelper::get('UI_FOOTER_ID', 0),
            "categorysectionid" => EnvHelper::get('UI_CATEGORY_SECTION_ID', 0),
            "categorycardid" => EnvHelper::get('UI_CATEGORY_CARD_ID', 3),
            "selectedBannerId" => EnvHelper::get('UI_SELECTED_BANNER_ID', 0),
            "breadcrumbId" => EnvHelper::get('UI_BREADCRUMB_ID', 2)
        ];
    }

    /**
     * Login/Authentication Konfigürasyonu
     * Bu bölüm farklı login tiplerini destekler:
     * - "phone": Telefon numarası + şifre
     * - "email": Email + şifre
     * - "otp": Telefon numarası + tek kullanımlık şifre (SMS)
     */
    public static function getLoginConfig()
    {
        return [
            "type" => EnvHelper::get('LOGIN_TYPE', 'phone'),
            "phoneFormat" => "TR",
            "requireEmailVerification" => EnvHelper::get('LOGIN_REQUIRE_EMAIL_VERIFICATION', false),
            "requirePhoneVerification" => EnvHelper::get('LOGIN_REQUIRE_PHONE_VERIFICATION', true),
            "minPasswordLength" => EnvHelper::get('LOGIN_MIN_PASSWORD_LENGTH', 6),
            "maxLoginAttempts" => EnvHelper::get('LOGIN_MAX_ATTEMPTS', 5),
            "lockoutDurationMinutes" => EnvHelper::get('LOGIN_LOCKOUT_DURATION_MINUTES', 30),
            "allowSocialLogin" => EnvHelper::get('LOGIN_ALLOW_SOCIAL', true),
            "socialProviders" => ["google"],
            "otp" => [
                "codeLength" => EnvHelper::get('OTP_CODE_LENGTH', 6),
                "expirationMinutes" => EnvHelper::get('OTP_EXPIRATION_MINUTES', 5),
                "resendCooldownSeconds" => EnvHelper::get('OTP_RESEND_COOLDOWN_SECONDS', 60),
                "maxAttempts" => EnvHelper::get('OTP_MAX_ATTEMPTS', 3),
                "smsProvider" => "default",
                "blockDurationMinutes" => EnvHelper::get('OTP_BLOCK_DURATION_MINUTES', 60)
            ],
            "email" => [
                "verificationRequired" => EnvHelper::get('EMAIL_VERIFICATION_REQUIRED', true),
                "verificationCodeLength" => EnvHelper::get('EMAIL_VERIFICATION_CODE_LENGTH', 6),
                "verificationExpirationMinutes" => EnvHelper::get('EMAIL_VERIFICATION_EXPIRATION_MINUTES', 30),
                "allowedDomains" => [], // Boş ise tüm domainler kabul edilir
                "blockedDomains" => ["tempmail.com", "10minutemail.com"]
            ],
            "phone" => [
                "verificationRequired" => EnvHelper::get('PHONE_VERIFICATION_REQUIRED', true),
                "allowedCountryCodes" => EnvHelper::getArray('PHONE_ALLOWED_COUNTRY_CODES', ['+90']),
                "minLength" => EnvHelper::get('PHONE_MIN_LENGTH', 10),
                "maxLength" => EnvHelper::get('PHONE_MAX_LENGTH', 11)
            ]
        ];
    }

    /**
     * SMS Konfigürasyonu (.env dosyasından okunur)
     */
    public static function getSMSConfig()
    {
        return [
            "provider" => EnvHelper::get('SMS_PROVIDER', 'netgsm'),
            "sender_name" => EnvHelper::get('SMS_SENDER_NAME', 'ZDUNYA'),
            "api_key" => EnvHelper::get('SMS_API_KEY'),
            "api_secret" => EnvHelper::get('SMS_API_SECRET'),
            "templates" => [
                "verification" => "Tek Kullanımlık Üyelik Doğrulama Şifreniz : {code} Lütfen Şifrenizi Kimse İle Paylaşmayınız.",
                "otp_login" => "Giriş kodunuz: {code} Bu kodu kimseyle paylaşmayın.",
                "password_reset" => "Şifre sıfırlama kodunuz: {code}"
            ],
            "rate_limits" => [
                "per_phone_per_hour" => EnvHelper::get('RATE_LIMIT_SMS_PER_PHONE_HOUR', 5),
                "per_ip_per_hour" => EnvHelper::get('RATE_LIMIT_SMS_PER_IP_HOUR', 20)
            ]
        ];
    }

    /**
     * Email Konfigürasyonu (.env dosyasından okunur)
     */
    public static function getEmailConfig()
    {
        return [
            "provider" => EnvHelper::get('EMAIL_PROVIDER', 'smtp'),
            "from_email" => EnvHelper::get('EMAIL_FROM_ADDRESS', '<EMAIL>'),
            "from_name" => EnvHelper::get('EMAIL_FROM_NAME', 'ZDunya E-Commerce'),
            "smtp_host" => EnvHelper::get('SMTP_HOST'),
            "smtp_port" => EnvHelper::get('SMTP_PORT', 587),
            "smtp_username" => EnvHelper::get('SMTP_USERNAME'),
            "smtp_password" => EnvHelper::get('SMTP_PASSWORD'),
            "smtp_encryption" => EnvHelper::get('SMTP_ENCRYPTION', 'tls'),
            "templates" => [
                "verification" => [
                    "subject" => "Email Doğrulama",
                    "template" => "email_verification"
                ],
                "password_reset" => [
                    "subject" => "Şifre Sıfırlama",
                    "template" => "password_reset"
                ],
                "welcome" => [
                    "subject" => "Hoş Geldiniz",
                    "template" => "welcome"
                ]
            ]
        ];
    }

    /**
     * Güvenlik Konfigürasyonu (.env dosyasından okunur - hassas bilgiler)
     */
    public static function getSecurityConfig()
    {
        return [
            "password_hash_algorithm" => PASSWORD_BCRYPT,
            "jwt_secret" => EnvHelper::get('JWT_SECRET', '48Scw74aAgf16wvAhhr85411A18w3'),
            "jwt_expiration_hours" => EnvHelper::get('JWT_EXPIRATION_HOURS', 12),
            "refresh_token_expiration_days" => EnvHelper::get('REFRESH_TOKEN_EXPIRATION_DAYS', 60),
            "encryption_key" => EnvHelper::get('ENCRYPTION_KEY', 'zdc.com.tr'),
            "session_timeout_minutes" => EnvHelper::get('SESSION_TIMEOUT_MINUTES', 30),
            "csrf_protection" => true,
            "rate_limiting" => [
                "login_attempts_per_ip_per_hour" => EnvHelper::get('RATE_LIMIT_LOGIN_PER_IP_HOUR', 20),
                "registration_attempts_per_ip_per_hour" => EnvHelper::get('RATE_LIMIT_REGISTRATION_PER_IP_HOUR', 10),
                "api_requests_per_minute" => EnvHelper::get('RATE_LIMIT_API_PER_MINUTE', 100)
            ]
        ];
    }

    /**
     * Veritabanı Konfigürasyonu (.env dosyasından okunur)
     */
    public static function getDatabaseConfig()
    {
        return [
            "host" => EnvHelper::get('DB_HOST', 'localhost'),
            "port" => EnvHelper::get('DB_PORT', 3306),
            "database" => EnvHelper::get('DB_DATABASE'),
            "username" => EnvHelper::get('DB_USERNAME'),
            "password" => EnvHelper::get('DB_PASSWORD'),
            "charset" => EnvHelper::get('DB_CHARSET', 'utf8mb4'),
            "collation" => EnvHelper::get('DB_COLLATION', 'utf8mb4_unicode_ci'),
            "connection_timeout" => 30,
            "query_timeout" => 60
        ];
    }

    /**
     * Tüm konfigürasyonu birleştiren ana metod (backend için)
     * @deprecated getBackendConfig() kullanın
     */
    public static function getAllConfig()
    {
        return self::getBackendConfig();
    }

    /**
     * Frontend için güvenli konfigürasyon verilerini döndürür
     * Sadece UI, login tipi ve kullanıcı deneyimi ile ilgili ayarları içerir
     */
    public static function getFrontendConfig()
    {
        return [
            // UI/UX ayarları
            "productCardId" => self::getUIConfig()["productCardId"],
            "showcaseSectionId" => self::getUIConfig()["showcaseSectionId"],
            "productCardColor" => self::getUIConfig()["productCardColor"],
            "productCardSecondaryColor" => self::getUIConfig()["productCardSecondaryColor"],
            "productCardBorderColor" => self::getUIConfig()["productCardBorderColor"],
            "productCardShadowColor" => self::getUIConfig()["productCardShadowColor"],
            "headerid" => self::getUIConfig()["headerid"],
            "footerid" => self::getUIConfig()["footerid"],
            "categorysectionid" => self::getUIConfig()["categorysectionid"],
            "categorycardid" => self::getUIConfig()["categorycardid"],
            "selectedBannerId" => self::getUIConfig()["selectedBannerId"],
            "breadcrumbId" => self::getUIConfig()["breadcrumbId"],

            // Login konfigürasyonu (sadece frontend'in bilmesi gerekenler)
            "loginType" => [
                "type" => self::getLoginConfig()["type"],
                "phoneFormat" => self::getLoginConfig()["phoneFormat"],
                "requireEmailVerification" => self::getLoginConfig()["requireEmailVerification"],
                "requirePhoneVerification" => self::getLoginConfig()["requirePhoneVerification"],
                "minPasswordLength" => self::getLoginConfig()["minPasswordLength"],
                "allowSocialLogin" => self::getLoginConfig()["allowSocialLogin"],
                "socialProviders" => self::getLoginConfig()["socialProviders"],
                "otp" => [
                    "codeLength" => self::getLoginConfig()["otp"]["codeLength"],
                    "expirationMinutes" => self::getLoginConfig()["otp"]["expirationMinutes"],
                    "resendCooldownSeconds" => self::getLoginConfig()["otp"]["resendCooldownSeconds"]
                ],
                "email" => [
                    "verificationRequired" => self::getLoginConfig()["email"]["verificationRequired"],
                    "verificationCodeLength" => self::getLoginConfig()["email"]["verificationCodeLength"],
                    "verificationExpirationMinutes" => self::getLoginConfig()["email"]["verificationExpirationMinutes"]
                ],
                "phone" => [
                    "verificationRequired" => self::getLoginConfig()["phone"]["verificationRequired"],
                    "minLength" => self::getLoginConfig()["phone"]["minLength"],
                    "maxLength" => self::getLoginConfig()["phone"]["maxLength"]
                ]
            ],

            // Uygulama genel bilgileri (güvenli olanlar)
            "appSettings" => [
                "app_name" => self::getAppSettings()["app_name"],
                "app_version" => self::getAppSettings()["app_version"],
                "environment" => self::getAppSettings()["environment"],
                "timezone" => self::getAppSettings()["timezone"],
                "language" => self::getAppSettings()["language"],
                "currency" => self::getAppSettings()["currency"]
            ]
        ];
    }

    /**
     * Backend için tüm konfigürasyon verilerini döndürür
     * Güvenlik, veritabanı ve iş kuralları ile ilgili hassas bilgileri içerir
     */
    public static function getBackendConfig()
    {
        return [
            // Güvenlik ayarları (hassas)
            "security" => self::getSecurityConfig(),

            // SMS ayarları (API anahtarları dahil)
            "sms" => self::getSMSConfig(),

            // Email ayarları (SMTP bilgileri dahil)
            "email" => self::getEmailConfig(),

            // Veritabanı ayarları
            "database" => self::getDatabaseConfig(),

            // Login ayarlarının tamamı (rate limiting, blok süreleri vs.)
            "login" => self::getLoginConfig(),

            // Uygulama ayarlarının tamamı
            "app" => self::getAppSettings()
        ];
    }

    /**
     * Web frontend için config data (eski fonksiyon - geriye uyumluluk için)
     * @deprecated getFrontendConfig() kullanın
     */
    public static function getWebConfigData()
    {
        return self::getFrontendConfig();
    }
}

/**
 * Web_get_config_data fonksiyonu - geriye uyumluluk için
 * Sadece frontend'in ihtiyacı olan konfigürasyon verilerini döndürür
 */
function web_get_config_data()
{
    $configData = AppConfig::getFrontendConfig();
    echo json_encode($configData);
}
