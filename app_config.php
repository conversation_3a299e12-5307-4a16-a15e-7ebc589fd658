<?php

/**
 * Uygulama Konfigürasyon Dosyası
 * Bu dosya tüm uygulama ayarlarını tek yerden yönetmek için oluşturulmuştur.
 * Farklı müşteriler için özelleştirme yapmak bu dosyayı düzenlemek yeterlidir.
 */

class AppConfig
{
    /**
     * Uygulama genel ayarları
     */
    public static function getAppSettings()
    {
        return [
            'app_name' => 'ZDunya E-Commerce',
            'app_version' => '1.0.0',
            'environment' => 'production', // development, staging, production
            'debug_mode' => false,
            'timezone' => 'Europe/Istanbul',
            'language' => 'tr',
            'currency' => 'TRY'
        ];
    }

    /**
     * UI/UX Konfigürasyonu
     */
    public static function getUIConfig()
    {
        return [
            "productCardId" => 2,
            "showcaseSectionId" => 0,
            "productCardColor" => "#ffffff",
            "productCardSecondaryColor" => "#f0f0f0",
            "productCardBorderColor" => "#dcdcdc",
            "productCardShadowColor" => "#aaaaaa",
            "headerid" => 2,
            "footerid" => 0,
            "categorysectionid" => 0,
            "categorycardid" => 3,
            "selectedBannerId" => 0,
            "breadcrumbId" => 2
        ];
    }

    /**
     * Login/Authentication Konfigürasyonu
     * Bu bölüm farklı login tiplerini destekler:
     * - "phone": Telefon numarası + şifre
     * - "email": Email + şifre
     * - "otp": Telefon numarası + tek kullanımlık şifre (SMS)
     */
    public static function getLoginConfig()
    {
        return [
            "type" => "phone", // phone, email, otp
            "phoneFormat" => "TR",
            "requireEmailVerification" => false,
            "requirePhoneVerification" => true,
            "minPasswordLength" => 6,
            "maxLoginAttempts" => 5,
            "lockoutDurationMinutes" => 30,
            "allowSocialLogin" => true,
            "socialProviders" => ["google"],
            "otp" => [
                "codeLength" => 6,
                "expirationMinutes" => 5,
                "resendCooldownSeconds" => 60,
                "maxAttempts" => 3,
                "smsProvider" => "default",
                "blockDurationMinutes" => 60
            ],
            "email" => [
                "verificationRequired" => true,
                "verificationCodeLength" => 6,
                "verificationExpirationMinutes" => 30,
                "allowedDomains" => [], // Boş ise tüm domainler kabul edilir
                "blockedDomains" => ["tempmail.com", "10minutemail.com"]
            ],
            "phone" => [
                "verificationRequired" => true,
                "allowedCountryCodes" => ["+90"], // Boş ise tüm ülke kodları kabul edilir
                "minLength" => 10,
                "maxLength" => 11
            ]
        ];
    }

    /**
     * SMS Konfigürasyonu
     */
    public static function getSMSConfig()
    {
        return [
            "provider" => "netgsm", // netgsm, twilio, custom
            "sender_name" => "ZDUNYA",
            "templates" => [
                "verification" => "Tek Kullanımlık Üyelik Doğrulama Şifreniz : {code} Lütfen Şifrenizi Kimse İle Paylaşmayınız.",
                "otp_login" => "Giriş kodunuz: {code} Bu kodu kimseyle paylaşmayın.",
                "password_reset" => "Şifre sıfırlama kodunuz: {code}"
            ],
            "rate_limits" => [
                "per_phone_per_hour" => 5,
                "per_ip_per_hour" => 20
            ]
        ];
    }

    /**
     * Email Konfigürasyonu
     */
    public static function getEmailConfig()
    {
        return [
            "provider" => "smtp", // smtp, sendgrid, mailgun
            "from_email" => "<EMAIL>",
            "from_name" => "ZDunya E-Commerce",
            "templates" => [
                "verification" => [
                    "subject" => "Email Doğrulama",
                    "template" => "email_verification"
                ],
                "password_reset" => [
                    "subject" => "Şifre Sıfırlama",
                    "template" => "password_reset"
                ],
                "welcome" => [
                    "subject" => "Hoş Geldiniz",
                    "template" => "welcome"
                ]
            ]
        ];
    }

    /**
     * Güvenlik Konfigürasyonu
     */
    public static function getSecurityConfig()
    {
        return [
            "password_hash_algorithm" => PASSWORD_BCRYPT,
            "jwt_secret" => "48Scw74aAgf16wvAhhr85411A18w3",
            "jwt_expiration_hours" => 12,
            "refresh_token_expiration_days" => 60,
            "encryption_key" => "zdc.com.tr",
            "session_timeout_minutes" => 30,
            "csrf_protection" => true,
            "rate_limiting" => [
                "login_attempts_per_ip_per_hour" => 20,
                "registration_attempts_per_ip_per_hour" => 10,
                "api_requests_per_minute" => 100
            ]
        ];
    }

    /**
     * Veritabanı Konfigürasyonu
     */
    public static function getDatabaseConfig()
    {
        return [
            "default_charset" => "utf8mb4",
            "default_collation" => "utf8mb4_unicode_ci",
            "connection_timeout" => 30,
            "query_timeout" => 60
        ];
    }

    /**
     * Tüm konfigürasyonu birleştiren ana metod (backend için)
     * @deprecated getBackendConfig() kullanın
     */
    public static function getAllConfig()
    {
        return self::getBackendConfig();
    }

    /**
     * Frontend için güvenli konfigürasyon verilerini döndürür
     * Sadece UI, login tipi ve kullanıcı deneyimi ile ilgili ayarları içerir
     */
    public static function getFrontendConfig()
    {
        return [
            // UI/UX ayarları
            "productCardId" => self::getUIConfig()["productCardId"],
            "showcaseSectionId" => self::getUIConfig()["showcaseSectionId"],
            "productCardColor" => self::getUIConfig()["productCardColor"],
            "productCardSecondaryColor" => self::getUIConfig()["productCardSecondaryColor"],
            "productCardBorderColor" => self::getUIConfig()["productCardBorderColor"],
            "productCardShadowColor" => self::getUIConfig()["productCardShadowColor"],
            "headerid" => self::getUIConfig()["headerid"],
            "footerid" => self::getUIConfig()["footerid"],
            "categorysectionid" => self::getUIConfig()["categorysectionid"],
            "categorycardid" => self::getUIConfig()["categorycardid"],
            "selectedBannerId" => self::getUIConfig()["selectedBannerId"],
            "breadcrumbId" => self::getUIConfig()["breadcrumbId"],

            // Login konfigürasyonu (sadece frontend'in bilmesi gerekenler)
            "loginType" => [
                "type" => self::getLoginConfig()["type"],
                "phoneFormat" => self::getLoginConfig()["phoneFormat"],
                "requireEmailVerification" => self::getLoginConfig()["requireEmailVerification"],
                "requirePhoneVerification" => self::getLoginConfig()["requirePhoneVerification"],
                "minPasswordLength" => self::getLoginConfig()["minPasswordLength"],
                "allowSocialLogin" => self::getLoginConfig()["allowSocialLogin"],
                "socialProviders" => self::getLoginConfig()["socialProviders"],
                "otp" => [
                    "codeLength" => self::getLoginConfig()["otp"]["codeLength"],
                    "expirationMinutes" => self::getLoginConfig()["otp"]["expirationMinutes"],
                    "resendCooldownSeconds" => self::getLoginConfig()["otp"]["resendCooldownSeconds"]
                ],
                "email" => [
                    "verificationRequired" => self::getLoginConfig()["email"]["verificationRequired"],
                    "verificationCodeLength" => self::getLoginConfig()["email"]["verificationCodeLength"],
                    "verificationExpirationMinutes" => self::getLoginConfig()["email"]["verificationExpirationMinutes"]
                ],
                "phone" => [
                    "verificationRequired" => self::getLoginConfig()["phone"]["verificationRequired"],
                    "minLength" => self::getLoginConfig()["phone"]["minLength"],
                    "maxLength" => self::getLoginConfig()["phone"]["maxLength"]
                ]
            ],

            // Uygulama genel bilgileri (güvenli olanlar)
            "appSettings" => [
                "app_name" => self::getAppSettings()["app_name"],
                "app_version" => self::getAppSettings()["app_version"],
                "environment" => self::getAppSettings()["environment"],
                "timezone" => self::getAppSettings()["timezone"],
                "language" => self::getAppSettings()["language"],
                "currency" => self::getAppSettings()["currency"]
            ]
        ];
    }

    /**
     * Backend için tüm konfigürasyon verilerini döndürür
     * Güvenlik, veritabanı ve iş kuralları ile ilgili hassas bilgileri içerir
     */
    public static function getBackendConfig()
    {
        return [
            // Güvenlik ayarları (hassas)
            "security" => self::getSecurityConfig(),

            // SMS ayarları (API anahtarları dahil)
            "sms" => self::getSMSConfig(),

            // Email ayarları (SMTP bilgileri dahil)
            "email" => self::getEmailConfig(),

            // Veritabanı ayarları
            "database" => self::getDatabaseConfig(),

            // Login ayarlarının tamamı (rate limiting, blok süreleri vs.)
            "login" => self::getLoginConfig(),

            // Uygulama ayarlarının tamamı
            "app" => self::getAppSettings()
        ];
    }

    /**
     * Web frontend için config data (eski fonksiyon - geriye uyumluluk için)
     * @deprecated getFrontendConfig() kullanın
     */
    public static function getWebConfigData()
    {
        return self::getFrontendConfig();
    }
}

/**
 * Web_get_config_data fonksiyonu - geriye uyumluluk için
 * Sadece frontend'in ihtiyacı olan konfigürasyon verilerini döndürür
 */
function web_get_config_data()
{
    $configData = AppConfig::getFrontendConfig();
    echo json_encode($configData);
}
