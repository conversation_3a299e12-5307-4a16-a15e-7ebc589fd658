# SQL Dosyaları

Bu klasör, veritabanı yapısı ve güncellemeleri için SQL dosyalarını içerir.

## Klas<PERSON>r Yapısı

- `tables/` - Tablo oluşturma ve güncelleme SQL dosyaları
  - `musteribilgi_create.sql` - <PERSON><PERSON><PERSON><PERSON> tablosunun temel yapısı
  - `musteribilgi_alter_login_types.sql` - Login tiplerini desteklemek için tablo güncellemeleri

## Kullanım

### 1. Yeni Kurulum
Yeni bir veritabanı kurulumu için önce temel tabloyu oluşturun:
```sql
-- musteribilgi_create.sql dosyasını çalıştırın
```

### 2. Mevcut Sistemi Güncelleme
Mevcut bir sistemi login tiplerini destekleyecek şekilde güncellemek için:
```sql
-- musteribilgi_alter_login_types.sql dosyasını çalıştırın
```

## Yeni Alan<PERSON>

Login tiplerini desteklemek için eklenen yeni alanlar:

### Email <PERSON>
- `email_verified` - <PERSON><PERSON> mı?
- `email_verification_code` - Email doğrulama kodu
- `email_verification_time` - Email doğrulama kodu gönderilme zamanı

### Telefon Doğrulama
- `phone_verified` - Telefon doğrulandı mı?

### OTP (One Time Password)
- `otp_code` - OTP kodu
- `otp_code_time` - OTP kodu gönderilme zamanı
- `otp_attempts` - OTP deneme sayısı
- `otp_blocked_until` - OTP bloklanma süresi

### Login Tercihleri
- `preferred_login_type` - Tercih edilen login tipi (phone, email, otp)
- `login_method_used` - Son kullanılan login yöntemi

### Güvenlik
- `failed_login_attempts` - Başarısız login deneme sayısı
- `account_locked_until` - Hesap kilitleme süresi
- `last_otp_request` - Son OTP talep zamanı

## Önemli Notlar

1. **Yedekleme**: SQL güncellemelerini çalıştırmadan önce mutlaka veritabanınızın yedeğini alın.

2. **Test Ortamı**: Önce test ortamında deneyin, sonra production'a uygulayın.

3. **İndeksler**: Yeni alanlar için gerekli indeksler otomatik olarak eklenir.

4. **Geriye Uyumluluk**: Mevcut alanlar değiştirilmez, sadece yeni alanlar eklenir.

## Sorun Giderme

Eğer SQL güncellemesi sırasında hata alırsanız:

1. Veritabanı kullanıcısının ALTER TABLE yetkisi olduğundan emin olun
2. Tablo üzerinde aktif bağlantı olmadığından emin olun
3. Yeterli disk alanı olduğundan emin olun
4. MySQL/MariaDB versiyonunuzun ENUM tipini desteklediğinden emin olun
