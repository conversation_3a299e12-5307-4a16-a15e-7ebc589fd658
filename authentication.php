<?php
cors_client();
require_once 'vendor/autoload.php';

use Nullix\CryptoJsAes\CryptoJsAes;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

require "js/aes/CryptoJsAes.php";
$is_debug = true;
// For security purposes, we can allow only specified agent type
$user_agent = $_SERVER['HTTP_USER_AGENT'];
if (preg_match('/python/i', $user_agent)) {
    echo 'You are forbidden!';
    foreach ($_POST as $key => $value) {
        error_log("Python Script Istegi : " . $key . "=" . $value, 0);
    }
    header('HTTP/1.0 403 Forbidden');
    return;
}
include_once('config.php');
include_once('app_config.php');
// **JSON yerine x-www-form-urlencoded ile veri alma**
if (strpos($_SERVER["CONTENT_TYPE"], "application/json") !== false) {
    $data = json_decode(file_get_contents('php://input'), true);
} else {
    $data = $_POST;
}
if (empty($data)) {
    $debug_info = $is_debug ? "Payload is empty or invalid format" : "";
    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;
}
$functName = $data['f'] ?? "";
if (!$functName) {
    $debug_info = $is_debug ? "Endpoint variable is not set" : "";
    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;
}
$validEndpoints = [
    "login_user",
    "logout_user",
    "register_user",
    "get_google_tokens",
    "refresh",
    "get_twitter_tokens",
    "send_otp",
    "verify_otp_login",
    "verify_email",
    "verify_phone",
    "resend_verification"
];
if (in_array($functName, $validEndpoints)) {
    if ($functName == "login_user") {
        // Login tipine göre farklı parametreler kullan
        if (isset($data['loginType']) && $data['loginType'] == 'email') {
            $functName($data['email'], $data['password'], 'email');
        } else {
            $functName($data['phoneNumber'], $data['password'], 'phone');
        }
    } else if ($functName == "logout_user") {
        $functName();
    } else if ($functName == "register_user") {
        // Register tipine göre farklı parametreler kullan
        if (isset($data['registerType']) && $data['registerType'] == 'email') {
            $functName($data['email'], $data['password'], $data['email'], isset($data['fullName']) ? $data['fullName'] : null, 'email');
        } else {
            $functName($data['phoneNumber'], $data['password'], isset($data['email']) ? $data['email'] : null, isset($data['fullName']) ? $data['fullName'] : null, 'phone');
        }
    } else if ($functName == "get_google_tokens") {
        $functName($data['code']);
    } else if ($functName == "refresh") {
        $functName();
    } else if ($functName == "get_twitter_tokens") {
        $functName();
    } else if ($functName == "send_otp") {
        send_otp($data['phoneNumber']);
    } else if ($functName == "verify_otp_login") {
        verify_otp_login($data['phoneNumber'], $data['otpCode']);
    } else if ($functName == "verify_email") {
        verify_email($data['email'], $data['code']);
    } else if ($functName == "verify_phone") {
        verify_phone($data['phoneNumber'], $data['code']);
    } else if ($functName == "resend_verification") {
        resend_verification($data['identifier'], $data['type']);
    }
} else {
    $debug_info = $is_debug ? "Endpoint Not Exists" : "";
    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
}
/* ============================================================================================== */
/*                                            endpoints                                           */
/* ============================================================================================== */
function log_jwt_contents($jwt_token)
{
    try {
        $jwt_parts = explode(".", $jwt_token);
        // Base64 decode the payload (middle part)
        $payload = base64_decode(str_replace(
            ['-', '_'],
            ['+', '/'],
            $jwt_parts[1]
        ));
        error_log("JWT Payload Raw: " . $payload);
        // Decode JSON
        $decoded = json_decode($payload, true);
        // Log each field separately
        foreach ($decoded as $key => $value) {
            error_log("JWT Field - {$key}: " . json_encode($value));
        }
        return $decoded;
    } catch (Exception $e) {
        error_log("Error decoding JWT: " . $e->getMessage());
        return null;
    }
}
function get_google_tokens($code)
{
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    $client_token = isset($data['code']) ? $data['code'] : null;
    if (!$client_token) {
        $response = new ErrorResult('Authorization code missing');
        $response->send(400);
        return;
    }
    $client = new Google_Client();
    $client->setClientId('231351638588-9m45dcrtfp5sslqisi22n39k520q1lv4.apps.googleusercontent.com');
    $client->setClientSecret('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');
    $client->setRedirectUri('https://coinscout.app');
    try {
        $token = $client->fetchAccessTokenWithAuthCode($client_token);
        if (isset($token['id_token'])) {
            $id_token = $token['id_token'];
            $payload = $client->verifyIdToken($id_token);
            if ($payload) {
                $googleId = $payload['sub'];
                $email = $payload['email'];
                $givenName = isset($payload['given_name']) ? $payload['given_name'] : null;
                $familyName = isset($payload['family_name']) ? $payload['family_name'] : null;
                $userInfo = null;
                if ($givenName && $familyName) {
                    $userInfo = $givenName . ' ' . $familyName;
                } elseif ($givenName) {
                    $userInfo = $givenName;
                } elseif ($familyName) {
                    $userInfo = $familyName;
                } else {
                    $userInfo = $email;
                }
                $googleUser = getOrCreateOauthUser($googleId, $email, null, RegistrationMethod::GOOGLE);
                $expires_at = time() + (60 * 60 * 24 * 60); // 30 gün
                $jwt = createAuthToken($googleUser['id']);
                $refresh_token = createRefreshToken($expires_at);
                $user_ip = $_SERVER['REMOTE_ADDR'];
                $userId = $googleUser['id'];
                if (!storeRefreshToken($userId, $refresh_token, $expires_at, $user_ip)) {
                    $response = new ErrorResult('Server Error');
                    $response->send(500);
                    return;
                }
                $response = new SuccessResult(['token' => $jwt, 'user' => $userInfo]);
                $response->send();
            } else {
                $response = new ErrorResult('Token doğrulama başarısız.');
                $response->send(400);
            }
        } else {
            $response = new ErrorResult('ID Token alınamadı.');
            $response->send(400);
        }
    } catch (Exception $e) {
        $response = new ErrorResult('Google Token Error: ' . $e->getMessage());
        $response->send(400);
    }
}
function get_twitter_tokens()
{
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    $client_token = isset($data['code']) ? $data['code'] : null;
    if (!$client_token) {
        $response = new ErrorResult('Authorization code missing');
        $response->send(400);
        return;
    }
    $twitter_client_id = 'M2RkWTBONHVvYTlMMTRkTzBLZlg6MTpjaQ';
    $twitter_client_secret = 'M9xq-XjNEkm8v4IsZezAHc1Ji8kS5PhzhWjc1aFeLeAmaq7xa4';
    $redirect_uri = 'https://coinscout.app/callback';
    // Code verifier'ı frontend'den almak gerekecek
    $code_verifier = isset($data['code_verifier']) ? $data['code_verifier'] : null;
    if (!$code_verifier) {
        $response = new ErrorResult('Code verifier missing');
        $response->send(400);
        return;
    }
    try {
        $token_url = 'https://api.twitter.com/2/oauth2/token';
        $token_data = [
            'code' => $client_token,
            'grant_type' => 'authorization_code',
            'client_id' => $twitter_client_id,
            'client_secret' => $twitter_client_secret,
            'redirect_uri' => $redirect_uri,
            'code_verifier' => $code_verifier
        ];
        $ch = curl_init($token_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Basic ' . base64_encode($twitter_client_id . ':' . $twitter_client_secret),
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ]);
        $token_response = curl_exec($ch);
        $token_data = json_decode($token_response, true);
        if (isset($token_data['access_token'])) {
            // Kullanıcı bilgilerini al
            $user_url = 'https://api.twitter.com/2/users/me';
            $user_ch = curl_init($user_url);
            curl_setopt($user_ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $token_data['access_token']
            ]);
            curl_setopt($user_ch, CURLOPT_RETURNTRANSFER, true);
            $user_response = curl_exec($user_ch);
            $user_data = json_decode($user_response, true);
            if (isset($user_data['data']['id'])) {
                $twitterId = $user_data['data']['id'];
                $username = $user_data['data']['username'];
                $name = $user_data['data']['name'];
                // Kullanıcıyı veritabanında kontrol et veya oluştur
                $twitterUser = getOrCreateOauthUser($twitterId, null, $username, RegistrationMethod::TWITTER);
                $expires_at = time() + (60 * 60 * 24 * 60); // 30 gün
                $jwt = createAuthToken($twitterUser['id']);
                $refresh_token = createRefreshToken($expires_at);
                $user_ip = $_SERVER['REMOTE_ADDR'];
                $userId = $twitterUser['id'];
                if (!storeRefreshToken($userId, $refresh_token, $expires_at, $user_ip)) {
                    $response = new ErrorResult('Server Error');
                    $response->send(500);
                    return;
                }
                $response = new SuccessResult([
                    'token' => $jwt,
                    'user' => $name
                ]);
                $response->send();
            } else {
                $response = new ErrorResult('Twitter kullanıcı bilgileri alınamadı.');
                $response->send(400);
            }
        } else {
            $response = new ErrorResult('Access Token alınamadı.');
            $response->send(400);
        }
    } catch (Exception $e) {
        $response = new ErrorResult('Twitter Token Error: ' . $e->getMessage());
        $response->send(400);
    }
}
function getOrCreateOauthUser($providerId, $providerEmail = null, $providerUsername = null, $registeredVia)
{
    global $link;
    $query = "SELECT * FROM users WHERE provider_user_id = ?";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "s", $providerId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user = mysqli_fetch_assoc($result);
    if ($user) {
        return $user;
    } else {
        mysqli_begin_transaction($link);
        try {
            // Yeni kullanıcıyı oluştur
            $insertQuery = "INSERT INTO users (provider_user_id, provider_username, registered_via, email)
                            VALUES (?, ?, ?, ?)";
            $insertStmt = mysqli_prepare($link, $insertQuery);
            mysqli_stmt_bind_param($insertStmt, "ssss", $providerId, $providerUsername, $registeredVia, $providerEmail);
            if (!mysqli_stmt_execute($insertStmt)) {
                throw new Exception('User registration failed.');
            }
            $userId = mysqli_insert_id($link);
            // My Watchlist oluştur
            $watchlistName = 'My Watchlist';
            $insertWatchlistQuery = "INSERT INTO watchlists (user_id, name) VALUES (?, ?)";
            $insertWatchlistStmt = mysqli_prepare($link, $insertWatchlistQuery);
            mysqli_stmt_bind_param($insertWatchlistStmt, "is", $userId, $watchlistName);
            if (!mysqli_stmt_execute($insertWatchlistStmt)) {
                throw new Exception('Watchlist creation failed.');
            }
            // My Portfolio oluştur
            $portfolioName = 'My Portfolio';
            $insertPortfolioQuery = "INSERT INTO portfolio (user_id, name) VALUES (?, ?)";
            $insertPortfolioStmt = mysqli_prepare($link, $insertPortfolioQuery);
            mysqli_stmt_bind_param($insertPortfolioStmt, "is", $userId, $portfolioName);
            if (!mysqli_stmt_execute($insertPortfolioStmt)) {
                throw new Exception('Portfolio creation failed.');
            }
            mysqli_commit($link);
            // Yeni kullanıcıyı sorgula
            $newUserQuery = "SELECT * FROM users WHERE provider_user_id = ?";
            $newUserStmt = mysqli_prepare($link, $newUserQuery);
            mysqli_stmt_bind_param($newUserStmt, "s", $providerId);
            mysqli_stmt_execute($newUserStmt);
            $newResult = mysqli_stmt_get_result($newUserStmt);
            $newUser = mysqli_fetch_assoc($newResult);
            return $newUser;
        } catch (Exception $e) {
            mysqli_rollback($link);
            $response = new ErrorResult($e->getMessage());
            $response->send(500);
            return;
        }
    }
}
function refresh()
{
    global $link;
    $headers = apache_request_headers();
    if (!isset($headers['Authorization']) || !isset($_COOKIE['refresh_token'])) {
        $response = new ErrorResult('Missing tokens.');
        $response->send(401);
        return;
    }
    // $authHeader = $headers['Authorization'];
    // $access_token = str_replace('Bearer ', '', $authHeader);
    $refresh_token = $_COOKIE['refresh_token'];
    // try {
    //     $decodedAccessToken = JWT::decode($access_token, new Key($secret_key, 'HS256'));
    //     $userId = $decodedAccessToken->userId;
    // } catch (Exception $e) {
    //     $response = new ErrorResult('Invalid or expired access token.');
    //     $response->send(401);
    //     return;
    // }
    $query = "SELECT token, user_id, expires FROM refresh_tokens WHERE token = ? AND revoked = 0";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s",  $refresh_token);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $refreshToken = mysqli_fetch_assoc($result);
        if ($refreshToken && strtotime($refreshToken['expires']) > time()) {
            $jwt = createAuthToken($refreshToken["user_id"]);
            $response = new SuccessResult(['token' => $jwt]);
            $response->send();
        } else {
            $response = new ErrorResult('Invalid or expired refresh token.');
            $response->send(401);
        }
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}
function login_with_otp($phoneNumber, $otpCode)
{
    global $link;
    $loginConfig = AppConfig::getLoginConfig();
    $otpConfig = $loginConfig['otp'];

    if (empty($phoneNumber) || empty($otpCode)) {
        $response = new ErrorResult('Phone number and OTP code are required.');
        $response->send(400);
        return;
    }

    // OTP kodunu ve süresini kontrol et
    $query = "SELECT id, otp_code, otp_code_time, otp_attempts, otp_blocked_until FROM musteribilgi WHERE tel = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $phoneNumber);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            // OTP blok kontrolü
            if ($user['otp_blocked_until'] && strtotime($user['otp_blocked_until']) > time()) {
                $response = new ErrorResult('OTP is temporarily blocked. Please try again later.');
                $response->send(423);
                return;
            }

            // OTP süresi kontrolü
            $otpTime = strtotime($user['otp_code_time']);
            $expirationTime = $otpTime + ($otpConfig['expirationMinutes'] * 60);

            if (time() > $expirationTime) {
                $response = new ErrorResult('OTP code has expired. Please request a new one.');
                $response->send(410);
                return;
            }

            // OTP kodu kontrolü
            if ($user['otp_code'] === $otpCode) {
                // Başarılı OTP - temizle ve login yap
                clear_otp_data($user['id']);

                // JWT ve refresh token oluştur
                $securityConfig = AppConfig::getSecurityConfig();
                $jwt = createAuthToken($user['id']);
                $expires_at = time() + (60 * 60 * 24 * $securityConfig['refresh_token_expiration_days']);
                $refresh_token = createRefreshToken($expires_at);
                $user_ip = $_SERVER['REMOTE_ADDR'];

                if (!storeRefreshToken($user['id'], $refresh_token, $expires_at, $user_ip)) {
                    $response = new ErrorResult('Server Error');
                    $response->send(500);
                    return;
                }

                update_last_login($user['id'], 'otp');

                $response = new SuccessResult(['token' => $jwt, 'user' => $phoneNumber]);
                $response->send();
            } else {
                // Yanlış OTP - attempt sayısını artır
                increment_otp_attempts($user['id'], $otpConfig['maxAttempts'], $otpConfig['blockDurationMinutes']);

                $response = new ErrorResult('Invalid OTP code.');
                $response->send(422);
            }
        } else {
            $response = new ErrorResult('User not found.');
            $response->send(422);
        }
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}

function login_user($identifier, $password, $loginType = null)
{
    // Login konfigürasyonunu al
    $loginConfig = AppConfig::getLoginConfig();
    // Frontend'den gelen loginType'ı kullan, yoksa config'den al
    if (!$loginType) {
        $loginType = $loginConfig['type'];
    }

    if (empty($identifier) || empty($password)) {
        $response = new ErrorResult('Identifier and password cannot be empty.');
        $response->send(400);
        return;
    }

    global $link;

    // Login tipine göre sorgu alanını belirle
    $queryField = '';
    switch ($loginType) {
        case 'email':
            $queryField = 'mail';
            break;
        case 'phone':
        case 'otp':
            $queryField = 'tel';
            break;
        default:
            $queryField = 'tel'; // Varsayılan olarak telefon
    }

    // OTP login için özel işlem
    if ($loginType === 'otp') {
        return login_with_otp($identifier, $password);
    }

    $query = "SELECT id, sifre, pw_version, isactivated, failed_login_attempts, account_locked_until FROM musteribilgi WHERE $queryField = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $identifier);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            // Hesap kilidi kontrolü
            if ($user['account_locked_until'] && strtotime($user['account_locked_until']) > time()) {
                $response = new ErrorResult('Account is temporarily locked. Please try again later.');
                $response->send(423);
                return;
            }

            // Aktivasyon kontrolü (email login için)
            if ($loginType === 'email' && $loginConfig['email']['verificationRequired'] && !$user['isactivated']) {
                $response = new ErrorResult('Please verify your email address first.');
                $response->send(403);
                return;
            }

            $isPasswordValid = false;

            // Eğer pw_version 2 ise mevcut şifreyi password_verify ile kontrol et
            if ($user['pw_version'] == 2) {
                $isPasswordValid = password_verify($password, $user['sifre']);
            }
            // Eğer pw_version 1 ise eski şifre kontrol yöntemini kullan
            else if ($user['pw_version'] == 1) {
                $password_db = $user['sifre'];
                $code = AppConfig::getSecurityConfig()['encryption_key'];
                $cryptor = new \RNCryptor\RNCryptor\Decryptor;
                try {
                    $sifre_dec_db = $cryptor->decrypt($password_db, $code);
                    if (strcmp($password, $sifre_dec_db) == 0) {
                        $isPasswordValid = true;
                    }
                } catch (Exception $e) {
                    $isPasswordValid = false;
                }
            }

            if ($isPasswordValid) {
                // Başarılı login - failed attempts'i sıfırla
                reset_failed_login_attempts($user['id']);

                // JWT ve refresh token oluştur
                $securityConfig = AppConfig::getSecurityConfig();
                $jwt = createAuthToken($user['id']);
                $expires_at = time() + (60 * 60 * 24 * $securityConfig['refresh_token_expiration_days']);
                $refresh_token = createRefreshToken($expires_at);
                $user_ip = $_SERVER['REMOTE_ADDR'];

                if (!storeRefreshToken($user['id'], $refresh_token, $expires_at, $user_ip)) {
                    $response = new ErrorResult('Server Error');
                    $response->send(500);
                    return;
                }

                // Son login zamanını güncelle
                update_last_login($user['id'], $loginType);

                $response = new SuccessResult(['token' => $jwt, 'user' => $identifier]);
                $response->send();
            } else {
                // Başarısız login - attempt sayısını artır
                increment_failed_login_attempts($user['id'], $loginConfig['maxLoginAttempts'], $loginConfig['lockoutDurationMinutes']);

                $response = new ErrorResult('Invalid credentials.');
                $response->send(422);
            }
        } else {
            $response = new ErrorResult('User not found.');
            $response->send(422);
        }
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}
function logout_user()
{
    global $link;
    if (isset($_COOKIE['auth_token2'])) {
        setcookie('auth_token2', '', [
            'expires' => time() - (60 * 60 * 48),
            'path' => '/',
            'domain' => ".zdc.com.tr",
            'secure' => true,
            'httponly' => true,
            'samesite' => 'None',
        ]);
    }
    if (isset($_COOKIE['refresh_token'])) {
        $refreshToken = $_COOKIE['refresh_token'];
        setcookie('refresh_token', '', [
            'expires' => time() - (60 * 60 * 48),
            'path' => '/',
            'domain' => ".zdc.com.tr",
            'secure' => true,
            'httponly' => true,
            'samesite' => 'None',
        ]);
        $user_ip = $_SERVER['REMOTE_ADDR'];

        $query = "UPDATE refresh_tokens SET revoked = 1, reason_revoked= 'logout', revoked_by_ip = ? WHERE token = ?";
        $stmt = mysqli_prepare($link, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "ss", $user_ip, $refreshToken);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        } else {
            error_log("Failed to prepare the SQL statement.");
        }
    }
    // Session Destroy
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_unset();
        session_destroy();
    }
    // Send SuccessResponse for logout
    $successResponse = new SuccessResult();
    $successResponse->send(200);  // 200 OK status code
    exit();
}
function register_user($identifier, $password, $email = null, $fullName = null, $registerType = null)
{
    global $link;

    // Login konfigürasyonunu al
    $loginConfig = AppConfig::getLoginConfig();
    // Frontend'den gelen registerType'ı kullan, yoksa config'den al
    $loginType = $registerType ? $registerType : $loginConfig['type'];

    if (empty($identifier) || empty($password)) {
        $response = new ErrorResult('Identifier and password are required.');
        $response->send(400);
        return;
    }

    // Şifre uzunluğu kontrolü
    if (strlen($password) < $loginConfig['minPasswordLength']) {
        $response = new ErrorResult('Password must be at least ' . $loginConfig['minPasswordLength'] . ' characters long.');
        $response->send(400);
        return;
    }

    // Login tipine göre alanları belirle
    $phoneNumber = '';
    $emailAddress = '';
    $queryField = '';

    switch ($loginType) {
        case 'email':
            $emailAddress = $identifier;
            $queryField = 'mail';
            // Email format kontrolü
            if (!filter_var($emailAddress, FILTER_VALIDATE_EMAIL)) {
                $response = new ErrorResult('Invalid email format.');
                $response->send(400);
                return;
            }
            // Blocked domains kontrolü
            $emailDomain = substr(strrchr($emailAddress, "@"), 1);
            if (in_array($emailDomain, $loginConfig['email']['blockedDomains'])) {
                $response = new ErrorResult('This email domain is not allowed.');
                $response->send(400);
                return;
            }
            break;
        case 'phone':
        case 'otp':
            $phoneNumber = $identifier;
            $queryField = 'tel';
            // Telefon format kontrolü
            if (strlen($phoneNumber) < $loginConfig['phone']['minLength'] ||
                strlen($phoneNumber) > $loginConfig['phone']['maxLength']) {
                $response = new ErrorResult('Invalid phone number format.');
                $response->send(400);
                return;
            }
            break;
    }

    // Mevcut kullanıcı kontrolü
    $query = "SELECT id FROM musteribilgi WHERE $queryField = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $identifier);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        if ($user) {
            $response = new ErrorResult('This ' . ($loginType === 'email' ? 'email' : 'phone number') . ' is already registered.');
            $response->send(409);
            return;
        }
    } else {
        $response = new ErrorResult('Database error (during check). ' . mysqli_error($link));
        $response->send(500);
        return;
    }

    mysqli_begin_transaction($link);
    try {
        $securityConfig = AppConfig::getSecurityConfig();
        $password_hash = password_hash($password, $securityConfig['password_hash_algorithm']);
        $date = date("Y-m-d H:i:s");
        $uid_token = md5(uniqid(mt_rand(), true));
        $os = "web";
        $fbid = "-1";

        // Aktivasyon durumu - email için doğrulama gerekiyorsa 0, diğerleri için 1
        $isactivated = ($loginType === 'email' && $loginConfig['email']['verificationRequired']) ? 0 : 1;

        // Doğrulama kodları
        $verification_code = rand(100000, 999999);
        $email_verification_code = ($loginType === 'email') ? rand(100000, 999999) : null;

        // Varsayılan değerler
        $adsoyad = $fullName ?: 'User';
        $finalEmail = $emailAddress ?: ($email ?: '<EMAIL>');
        $finalPhone = $phoneNumber ?: '';

        $query = "INSERT INTO musteribilgi (
            adsoyad, tel, mail, sifre, uid, register_date,
            verification_code, verification_code_time,
            email_verification_code, email_verification_time,
            os, appleid, isactivated, pw_version, preferred_login_type
        ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        $stmt = mysqli_prepare($link, $query);
        if (!$stmt) {
            throw new Exception('Database error (during user insertion). ' . mysqli_error($link));
        }

        $pw_version = 2; // Yeni kullanıcılar için bcrypt
        mysqli_stmt_bind_param($stmt, 'ssssssssssssiis',
            $adsoyad, $finalPhone, $finalEmail, $password_hash, $uid_token, $date,
            $verification_code, $date, $email_verification_code, $date,
            $os, $fbid, $isactivated, $pw_version, $loginType
        );

        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception('User registration failed.' . mysqli_error($link));
        }

        $userId = mysqli_insert_id($link);
        mysqli_commit($link);

        // Doğrulama kodu gönder
        if ($loginType === 'email' && $loginConfig['email']['verificationRequired']) {
            send_email_verification($finalEmail, $email_verification_code);
            $response = new SuccessResult(['message' => 'Registration successful. Please check your email for verification code.', 'uid' => $uid_token]);
        } else if ($loginType === 'phone' && $loginConfig['phone']['verificationRequired']) {
            send_sms_verification($finalPhone, $verification_code);
            $response = new SuccessResult(['message' => 'Registration successful. Please check your phone for verification code.', 'uid' => $uid_token]);
        } else {
            $response = new SuccessResult(['message' => 'Registration successful.', 'uid' => $uid_token]);
        }

        $response->send();

    } catch (Exception $e) {
        mysqli_rollback($link);
        $response = new ErrorResult($e->getMessage());
        $response->send(500);
    }
}
function createRefreshToken($expires_at)
{
    $refresh_token = bin2hex(random_bytes(64));
    setcookie(
        "refresh_token",
        $refresh_token,
        [
            'expires' => $expires_at,
            'path' => "/",
            'domain' => ".zdc.com.tr",
            'secure' => true,
            'httponly' => true,
            'samesite' => 'None'
        ]
    );
    return $refresh_token;
}
function createAuthToken($userId)
{
    $secret_key = "48Scw74aAgf16wvAhhr85411A18w3";
    $payload = [
        'iss' => ".zdc.com.tr",
        'aud' => ".zdc.com.tr",
        'iat' => time(),
        'exp' => time() + 60 * 60 * 12,
        'userId' => $userId,
        "permissions" => [2, 4, 5, 7]
    ];
    $jwt = JWT::encode($payload, $secret_key, 'HS256');
    setcookie(
        "auth_token2",
        $jwt,
        [
            'expires' => time() + 60 * 5,
            'path' => "/",
            'domain' => ".zdc.com.tr",
            'secure' => true,
            'httponly' => false,
            'samesite' => 'None'
        ]
    );
    return $jwt;
}
function storeRefreshToken($userId, $refresh_token, $expires_at, $user_ip)
{
    global $link;
    $insert_query = "INSERT INTO refresh_tokens (user_id, token, expires, created_by_ip) VALUES (?, ?, FROM_UNIXTIME(?), ?)";
    $stmt_refresh = mysqli_prepare($link, $insert_query);
    if ($stmt_refresh) {
        mysqli_stmt_bind_param($stmt_refresh, "isis", $userId, $refresh_token, $expires_at, $user_ip);
        return mysqli_stmt_execute($stmt_refresh);
    }
    return false;
}
/* ============================================================================================== */
/*                                        Authentication Helper Functions                        */
/* ============================================================================================== */

function clear_otp_data($userId)
{
    global $link;
    $query = "UPDATE musteribilgi SET otp_code = NULL, otp_code_time = NULL, otp_attempts = 0 WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
    }
}

function increment_otp_attempts($userId, $maxAttempts, $blockDurationMinutes)
{
    global $link;
    $query = "UPDATE musteribilgi SET otp_attempts = otp_attempts + 1 WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);

        // Max attempts'e ulaştıysa blokla
        $checkQuery = "SELECT otp_attempts FROM musteribilgi WHERE id = ?";
        $checkStmt = mysqli_prepare($link, $checkQuery);
        if ($checkStmt) {
            mysqli_stmt_bind_param($checkStmt, "i", $userId);
            mysqli_stmt_execute($checkStmt);
            $result = mysqli_stmt_get_result($checkStmt);
            $user = mysqli_fetch_assoc($result);

            if ($user && $user['otp_attempts'] >= $maxAttempts) {
                $blockUntil = date('Y-m-d H:i:s', time() + ($blockDurationMinutes * 60));
                $blockQuery = "UPDATE musteribilgi SET otp_blocked_until = ? WHERE id = ?";
                $blockStmt = mysqli_prepare($link, $blockQuery);
                if ($blockStmt) {
                    mysqli_stmt_bind_param($blockStmt, "si", $blockUntil, $userId);
                    mysqli_stmt_execute($blockStmt);
                }
            }
        }
    }
}

function reset_failed_login_attempts($userId)
{
    global $link;
    $query = "UPDATE musteribilgi SET failed_login_attempts = 0, account_locked_until = NULL WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
    }
}

function increment_failed_login_attempts($userId, $maxAttempts, $lockoutDurationMinutes)
{
    global $link;
    $query = "UPDATE musteribilgi SET failed_login_attempts = failed_login_attempts + 1 WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);

        // Max attempts'e ulaştıysa hesabı kilitle
        $checkQuery = "SELECT failed_login_attempts FROM musteribilgi WHERE id = ?";
        $checkStmt = mysqli_prepare($link, $checkQuery);
        if ($checkStmt) {
            mysqli_stmt_bind_param($checkStmt, "i", $userId);
            mysqli_stmt_execute($checkStmt);
            $result = mysqli_stmt_get_result($checkStmt);
            $user = mysqli_fetch_assoc($result);

            if ($user && $user['failed_login_attempts'] >= $maxAttempts) {
                $lockUntil = date('Y-m-d H:i:s', time() + ($lockoutDurationMinutes * 60));
                $lockQuery = "UPDATE musteribilgi SET account_locked_until = ? WHERE id = ?";
                $lockStmt = mysqli_prepare($link, $lockQuery);
                if ($lockStmt) {
                    mysqli_stmt_bind_param($lockStmt, "si", $lockUntil, $userId);
                    mysqli_stmt_execute($lockStmt);
                }
            }
        }
    }
}

function update_last_login($userId, $loginMethod)
{
    global $link;
    $now = date('Y-m-d H:i:s');
    $query = "UPDATE musteribilgi SET last_login = ?, login_count = login_count + 1, login_method_used = ? WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "ssi", $now, $loginMethod, $userId);
        mysqli_stmt_execute($stmt);
    }
}

function send_sms_verification($phoneNumber, $code)
{
    $smsConfig = AppConfig::getSMSConfig();
    $message = str_replace('{code}', $code, $smsConfig['templates']['verification']);

    // SMS gönderme işlemi - mevcut SMSGonder fonksiyonunu kullan
    if (function_exists('SMSGonder')) {
        $raw_phone = preg_replace("/[^0-9]/", "", $phoneNumber);
        SMSGonder($raw_phone, $message);
    }
}

function send_email_verification($email, $code)
{
    $emailConfig = AppConfig::getEmailConfig();
    $subject = $emailConfig['templates']['verification']['subject'];
    $message = "Your verification code is: " . $code;

    // Email gönderme işlemi - basit mail() fonksiyonu kullanılabilir
    // Gerçek uygulamada PHPMailer veya benzeri kullanılmalı
    mail($email, $subject, $message, "From: " . $emailConfig['from_email']);
}

function verify_email($email, $code)
{
    global $link;

    if (empty($email) || empty($code)) {
        $response = new ErrorResult('Email and verification code are required.');
        $response->send(400);
        return;
    }

    $query = "SELECT id, email_verification_code, email_verification_time FROM musteribilgi WHERE mail = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $email);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            $loginConfig = AppConfig::getLoginConfig();
            $expirationMinutes = $loginConfig['email']['verificationExpirationMinutes'];

            // Kod süresi kontrolü
            $codeTime = strtotime($user['email_verification_time']);
            $expirationTime = $codeTime + ($expirationMinutes * 60);

            if (time() > $expirationTime) {
                $response = new ErrorResult('Verification code has expired. Please request a new one.');
                $response->send(410);
                return;
            }

            // Kod kontrolü
            if ($user['email_verification_code'] === $code) {
                // Email doğrulama başarılı
                $updateQuery = "UPDATE musteribilgi SET email_verified = 1, isactivated = 1 WHERE id = ?";
                $updateStmt = mysqli_prepare($link, $updateQuery);
                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "i", $user['id']);
                    mysqli_stmt_execute($updateStmt);

                    $response = new SuccessResult(['message' => 'Email verified successfully.']);
                    $response->send();
                } else {
                    $response = new ErrorResult('Failed to verify email.');
                    $response->send(500);
                }
            } else {
                $response = new ErrorResult('Invalid verification code.');
                $response->send(422);
            }
        } else {
            $response = new ErrorResult('User not found.');
            $response->send(422);
        }
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}

function verify_phone($phoneNumber, $code)
{
    global $link;

    if (empty($phoneNumber) || empty($code)) {
        $response = new ErrorResult('Phone number and verification code are required.');
        $response->send(400);
        return;
    }

    $query = "SELECT id, verification_code, verification_code_time FROM musteribilgi WHERE tel = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $phoneNumber);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            $loginConfig = AppConfig::getLoginConfig();
            $expirationMinutes = 30; // Varsayılan değer

            // Kod süresi kontrolü
            $codeTime = strtotime($user['verification_code_time']);
            $expirationTime = $codeTime + ($expirationMinutes * 60);

            if (time() > $expirationTime) {
                $response = new ErrorResult('Verification code has expired. Please request a new one.');
                $response->send(410);
                return;
            }

            // Kod kontrolü
            if ($user['verification_code'] === $code) {
                // Telefon doğrulama başarılı
                $updateQuery = "UPDATE musteribilgi SET phone_verified = 1, isactivated = 1 WHERE id = ?";
                $updateStmt = mysqli_prepare($link, $updateQuery);
                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "i", $user['id']);
                    mysqli_stmt_execute($updateStmt);

                    $response = new SuccessResult(['message' => 'Phone verified successfully.']);
                    $response->send();
                } else {
                    $response = new ErrorResult('Failed to verify phone.');
                    $response->send(500);
                }
            } else {
                $response = new ErrorResult('Invalid verification code.');
                $response->send(422);
            }
        } else {
            $response = new ErrorResult('User not found.');
            $response->send(422);
        }
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}

function resend_verification($identifier, $type)
{
    global $link;

    if (empty($identifier) || empty($type)) {
        $response = new ErrorResult('Identifier and type are required.');
        $response->send(400);
        return;
    }

    $field = ($type === 'email') ? 'mail' : 'tel';
    $query = "SELECT id FROM musteribilgi WHERE $field = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $identifier);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            $now = date('Y-m-d H:i:s');
            $code = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);

            if ($type === 'email') {
                $updateQuery = "UPDATE musteribilgi SET email_verification_code = ?, email_verification_time = ? WHERE id = ?";
                $updateStmt = mysqli_prepare($link, $updateQuery);
                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "ssi", $code, $now, $user['id']);
                    mysqli_stmt_execute($updateStmt);

                    send_email_verification($identifier, $code);
                    $response = new SuccessResult(['message' => 'Verification email sent.']);
                    $response->send();
                }
            } else {
                $updateQuery = "UPDATE musteribilgi SET verification_code = ?, verification_code_time = ? WHERE id = ?";
                $updateStmt = mysqli_prepare($link, $updateQuery);
                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "ssi", $code, $now, $user['id']);
                    mysqli_stmt_execute($updateStmt);

                    send_sms_verification($identifier, $code);
                    $response = new SuccessResult(['message' => 'Verification SMS sent.']);
                    $response->send();
                }
            }
        } else {
            $response = new ErrorResult('User not found.');
            $response->send(422);
        }
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}

function send_otp($phoneNumber)
{
    global $link;
    $loginConfig = AppConfig::getLoginConfig();
    $otpConfig = $loginConfig['otp'];

    if (empty($phoneNumber)) {
        $response = new ErrorResult('Phone number is required.');
        $response->send(400);
        return;
    }

    // Telefon numarasını temizle
    $cleanPhone = preg_replace("/[^0-9]/", "", $phoneNumber);

    // Kullanıcının var olup olmadığını kontrol et
    $userQuery = "SELECT id FROM musteribilgi WHERE tel = ?";
    $userStmt = mysqli_prepare($link, $userQuery);
    if ($userStmt) {
        mysqli_stmt_bind_param($userStmt, "s", $phoneNumber);
        mysqli_stmt_execute($userStmt);
        $userResult = mysqli_stmt_get_result($userStmt);
        $user = mysqli_fetch_assoc($userResult);

        if (!$user) {
            $response = new ErrorResult('User not found. Please register first.');
            $response->send(404);
            return;
        }
    }

    // Rate limiting kontrolü - son OTP isteğinden beri geçen süre
    $rateLimitQuery = "SELECT created_at FROM otp_codes WHERE phone_number = ? ORDER BY created_at DESC LIMIT 1";
    $rateLimitStmt = mysqli_prepare($link, $rateLimitQuery);
    if ($rateLimitStmt) {
        mysqli_stmt_bind_param($rateLimitStmt, "s", $cleanPhone);
        mysqli_stmt_execute($rateLimitStmt);
        $rateLimitResult = mysqli_stmt_get_result($rateLimitStmt);
        $lastOtp = mysqli_fetch_assoc($rateLimitResult);

        if ($lastOtp) {
            $lastRequest = strtotime($lastOtp['created_at']);
            $cooldown = $otpConfig['resendCooldownSeconds'];

            if (time() - $lastRequest < $cooldown) {
                $response = new ErrorResult('Please wait before requesting another OTP.');
                $response->send(429);
                return;
            }
        }
    }

    // Eski OTP kodlarını geçersiz kıl
    $invalidateQuery = "UPDATE otp_codes SET is_used = TRUE WHERE phone_number = ? AND is_used = FALSE";
    $invalidateStmt = mysqli_prepare($link, $invalidateQuery);
    if ($invalidateStmt) {
        mysqli_stmt_bind_param($invalidateStmt, "s", $cleanPhone);
        mysqli_stmt_execute($invalidateStmt);
    }

    // Yeni OTP kodu oluştur
    $otpCode = str_pad(rand(0, pow(10, $otpConfig['codeLength']) - 1), $otpConfig['codeLength'], '0', STR_PAD_LEFT);
    $expiresAt = date('Y-m-d H:i:s', time() + ($otpConfig['expirationMinutes'] * 60));

    // OTP kodunu veritabanına kaydet
    $insertQuery = "INSERT INTO otp_codes (phone_number, otp_code, expires_at) VALUES (?, ?, ?)";
    $insertStmt = mysqli_prepare($link, $insertQuery);
    if ($insertStmt) {
        mysqli_stmt_bind_param($insertStmt, "sss", $cleanPhone, $otpCode, $expiresAt);
        if (mysqli_stmt_execute($insertStmt)) {
            // SMS gönder
            send_sms_verification($phoneNumber, $otpCode);

            $response = new SuccessResult(['message' => 'OTP sent successfully.']);
            $response->send();
        } else {
            $response = new ErrorResult('Failed to generate OTP.');
            $response->send(500);
        }
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}

function verify_otp_login($phoneNumber, $otpCode)
{
    global $link;
    $loginConfig = AppConfig::getLoginConfig();
    $otpConfig = $loginConfig['otp'];

    if (empty($phoneNumber) || empty($otpCode)) {
        $response = new ErrorResult('Phone number and OTP code are required.');
        $response->send(400);
        return;
    }

    // Telefon numarasını temizle
    $cleanPhone = preg_replace("/[^0-9]/", "", $phoneNumber);

    // Kullanıcıyı bul
    $userQuery = "SELECT id FROM musteribilgi WHERE tel = ?";
    $userStmt = mysqli_prepare($link, $userQuery);
    if ($userStmt) {
        mysqli_stmt_bind_param($userStmt, "s", $phoneNumber);
        mysqli_stmt_execute($userStmt);
        $userResult = mysqli_stmt_get_result($userStmt);
        $user = mysqli_fetch_assoc($userResult);

        if (!$user) {
            $response = new ErrorResult('User not found.');
            $response->send(404);
            return;
        }
    }

    // OTP kodunu kontrol et
    $otpQuery = "SELECT id, attempts FROM otp_codes WHERE phone_number = ? AND otp_code = ? AND expires_at > NOW() AND is_used = FALSE ORDER BY created_at DESC LIMIT 1";
    $otpStmt = mysqli_prepare($link, $otpQuery);
    if ($otpStmt) {
        mysqli_stmt_bind_param($otpStmt, "ss", $cleanPhone, $otpCode);
        mysqli_stmt_execute($otpStmt);
        $otpResult = mysqli_stmt_get_result($otpStmt);
        $otpRecord = mysqli_fetch_assoc($otpResult);

        if ($otpRecord) {
            // OTP doğru - kullanıldı olarak işaretle
            $markUsedQuery = "UPDATE otp_codes SET is_used = TRUE WHERE id = ?";
            $markUsedStmt = mysqli_prepare($link, $markUsedQuery);
            if ($markUsedStmt) {
                mysqli_stmt_bind_param($markUsedStmt, "i", $otpRecord['id']);
                mysqli_stmt_execute($markUsedStmt);
            }

            // JWT ve refresh token oluştur
            $securityConfig = AppConfig::getSecurityConfig();
            $jwt = createAuthToken($user['id']);
            $expires_at = time() + (60 * 60 * 24 * $securityConfig['refresh_token_expiration_days']);
            $refresh_token = createRefreshToken($expires_at);
            $user_ip = $_SERVER['REMOTE_ADDR'];

            if (!storeRefreshToken($user['id'], $refresh_token, $expires_at, $user_ip)) {
                $response = new ErrorResult('Server Error');
                $response->send(500);
                return;
            }

            // Son login zamanını güncelle
            update_last_login($user['id'], 'otp');

            $response = new SuccessResult(['token' => $jwt, 'user' => $phoneNumber]);
            $response->send();
        } else {
            // Yanlış OTP - attempt sayısını artır
            $incrementQuery = "UPDATE otp_codes SET attempts = attempts + 1 WHERE phone_number = ? AND otp_code = ? AND is_used = FALSE";
            $incrementStmt = mysqli_prepare($link, $incrementQuery);
            if ($incrementStmt) {
                mysqli_stmt_bind_param($incrementStmt, "ss", $cleanPhone, $otpCode);
                mysqli_stmt_execute($incrementStmt);
            }

            $response = new ErrorResult('Invalid or expired OTP code.');
            $response->send(422);
        }
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}

function generate_otp($phoneNumber)
{
    global $link;
    $loginConfig = AppConfig::getLoginConfig();
    $otpConfig = $loginConfig['otp'];

    // Rate limiting kontrolü
    $query = "SELECT last_otp_request FROM musteribilgi WHERE tel = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $phoneNumber);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user && $user['last_otp_request']) {
            $lastRequest = strtotime($user['last_otp_request']);
            $cooldown = $otpConfig['resendCooldownSeconds'];

            if (time() - $lastRequest < $cooldown) {
                $response = new ErrorResult('Please wait before requesting another OTP.');
                $response->send(429);
                return;
            }
        }
    }

    // OTP kodu oluştur
    $otpCode = str_pad(rand(0, pow(10, $otpConfig['codeLength']) - 1), $otpConfig['codeLength'], '0', STR_PAD_LEFT);
    $now = date('Y-m-d H:i:s');

    // OTP kodunu kaydet
    $updateQuery = "UPDATE musteribilgi SET otp_code = ?, otp_code_time = ?, last_otp_request = ?, otp_attempts = 0 WHERE tel = ?";
    $updateStmt = mysqli_prepare($link, $updateQuery);
    if ($updateStmt) {
        mysqli_stmt_bind_param($updateStmt, "ssss", $otpCode, $now, $now, $phoneNumber);
        mysqli_stmt_execute($updateStmt);

        // SMS gönder
        send_sms_verification($phoneNumber, $otpCode);

        $response = new SuccessResult(['message' => 'OTP sent successfully.']);
        $response->send();
    } else {
        $response = new ErrorResult('Failed to generate OTP.');
        $response->send(500);
    }
}

/* ============================================================================================== */
/*                                        Utility Functions                                       */
/* ============================================================================================== */
function cors_client()
{
    // Allow from any origin
    if (isset($_SERVER['HTTP_ORIGIN'])) {
        $allowed_origins = [
            'http://localhost:3000',
            'http://localhost:5000',
        ];
        if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'], $allowed_origins)) {
            header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
            header("Access-Control-Allow-Credentials: true");
        }
        header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');    // cache for 1 day
    }
    // Access-Control headers are received during OPTIONS requests
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
            // may also be using PUT, PATCH, HEAD etc
            header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        }
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
            header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        }
        exit(0);
    }
}
function affiliateGuid()
{
    if (function_exists('com_create_guid') === true) {
        return trim(com_create_guid(), '{}');
    }
    return strtolower(sprintf('%04X%04X-%04X-%04X-%04X-%04X%04X%04X', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(16384, 20479), mt_rand(32768, 49151), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535)));
}
/* ============================================================================================== */
/*                                             classes                                            */
/* ============================================================================================== */
class RegistrationMethod
{
    const GOOGLE = 'google';
    const EMAIL = 'email';
    const TWITTER = 'twitter';
}
class SuccessResult
{
    public $success;
    public $output;
    public function __construct($data = null)
    {
        $this->success = true;
        $this->output = $data;
    }
    public function send($code = 200)
    {
        http_response_code($code);
        echo json_encode($this, JSON_PRETTY_PRINT);
    }
}
class ErrorResult
{
    public $success;
    public $errormsg;
    public function __construct($errormsg)
    {
        $this->success = false;
        $this->errormsg = $errormsg;
    }
    public function send($code = 500)
    {
        http_response_code($code);
        echo json_encode($this, JSON_PRETTY_PRINT);
    }
}
