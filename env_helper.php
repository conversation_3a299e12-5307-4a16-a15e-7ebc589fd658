<?php
/**
 * Environment Variables Helper
 * .env dosyalarını okumak ve environment variable'ları yönetmek için
 */

class EnvHelper 
{
    private static $loaded = false;
    private static $envVars = [];

    /**
     * .env dosyasını yükle
     */
    public static function load($envFile = '.env')
    {
        if (self::$loaded) {
            return;
        }

        $envPath = __DIR__ . '/' . $envFile;
        
        if (!file_exists($envPath)) {
            // .env dosyası yoksa .env.example'dan kopyala
            $examplePath = __DIR__ . '/.env.example';
            if (file_exists($examplePath)) {
                copy($examplePath, $envPath);
                error_log("Warning: .env file created from .env.example. Please update with your actual values.");
            } else {
                error_log("Warning: Neither .env nor .env.example file found.");
                return;
            }
        }

        $lines = file($envPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // Yorum satırlarını atla
            if (strpos(trim($line), '#') === 0) {
                continue;
            }

            // KEY=VALUE formatını parse et
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Tırnak işaretlerini temizle
                $value = trim($value, '"\'');
                
                // Environment variable'a set et
                if (!array_key_exists($key, $_ENV)) {
                    $_ENV[$key] = $value;
                    putenv("$key=$value");
                }
                
                // Internal cache'e de ekle
                self::$envVars[$key] = $value;
            }
        }

        self::$loaded = true;
    }

    /**
     * Environment variable değerini al
     */
    public static function get($key, $default = null)
    {
        self::load();
        
        // Önce $_ENV'den kontrol et
        if (array_key_exists($key, $_ENV)) {
            return self::parseValue($_ENV[$key]);
        }
        
        // Sonra getenv() ile kontrol et
        $value = getenv($key);
        if ($value !== false) {
            return self::parseValue($value);
        }
        
        // Internal cache'den kontrol et
        if (array_key_exists($key, self::$envVars)) {
            return self::parseValue(self::$envVars[$key]);
        }
        
        return $default;
    }

    /**
     * Boolean değerleri parse et
     */
    private static function parseValue($value)
    {
        if (is_string($value)) {
            $lower = strtolower($value);
            if (in_array($lower, ['true', '1', 'yes', 'on'])) {
                return true;
            }
            if (in_array($lower, ['false', '0', 'no', 'off', ''])) {
                return false;
            }
            if (is_numeric($value)) {
                return strpos($value, '.') !== false ? (float)$value : (int)$value;
            }
        }
        
        return $value;
    }

    /**
     * Gerekli environment variable'ların set olup olmadığını kontrol et
     */
    public static function validateRequired($requiredKeys)
    {
        $missing = [];
        
        foreach ($requiredKeys as $key) {
            if (self::get($key) === null) {
                $missing[] = $key;
            }
        }
        
        if (!empty($missing)) {
            throw new Exception('Missing required environment variables: ' . implode(', ', $missing));
        }
    }

    /**
     * Array formatındaki değerleri parse et (örn: "+90,+1,+44")
     */
    public static function getArray($key, $default = [], $separator = ',')
    {
        $value = self::get($key);
        
        if ($value === null) {
            return $default;
        }
        
        if (is_string($value)) {
            $array = explode($separator, $value);
            return array_map('trim', $array);
        }
        
        return $default;
    }

    /**
     * Tüm environment variable'ları döndür (debug için)
     */
    public static function all()
    {
        self::load();
        return array_merge(self::$envVars, $_ENV);
    }
}

// Otomatik yükleme
EnvHelper::load();
?>
