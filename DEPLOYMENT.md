# Deployment Rehberi

Bu rehber, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> farklı ortamlarda nasıl deploy edileceğini açıklar.

## Gereksinimler

- PHP 7.4 veya üzeri
- MySQL 5.7 veya üzeri / MariaDB 10.2 veya üzeri
- Web server (Apache/Nginx)

## Kurulum Adımları

### 1. Kod De<PERSON><PERSON>

```bash
git clone <repository-url>
cd zdunya-backend
```

### 2. Environment Dosyasını Oluşturun

```bash
# .env.example dosyasını kopyalayın
cp .env.example .env

# .env dosyasını düzenleyin
nano .env
```

### 3. Gerekli Değerleri Ayarlayın

`.env` dosyasında aşağıdaki değerleri mutlaka güncelleyin:

#### Güvenlik (ÖNEMLİ!)
```env
JWT_SECRET="your-super-secret-jwt-key-here-min-32-chars"
ENCRYPTION_KEY="your-encryption-key-here"
```

#### Veritabanı
```env
DB_HOST=localhost
DB_DATABASE=zdunya_db
DB_USERNAME=zdunya_user
DB_PASSWORD="your-secure-password"
```

#### SMS Ayarları
```env
SMS_API_KEY="your-sms-api-key"
SMS_API_SECRET="your-sms-api-secret"
```

#### Email Ayarları
```env
SMTP_HOST="smtp.example.com"
SMTP_USERNAME="your-smtp-username"
SMTP_PASSWORD="your-smtp-password"
```

### 4. Veritabanını Kurun

```sql
-- 1. Veritabanını oluşturun
CREATE DATABASE zdunya_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. Kullanıcı oluşturun
CREATE USER 'zdunya_user'@'localhost' IDENTIFIED BY 'your-secure-password';
GRANT ALL PRIVILEGES ON zdunya_db.* TO 'zdunya_user'@'localhost';
FLUSH PRIVILEGES;

-- 3. Tabloları oluşturun
-- sql/tables/musteribilgi_create.sql dosyasını çalıştırın
-- sql/tables/musteribilgi_alter_login_types.sql dosyasını çalıştırın
-- sql/tables/otp_codes_create.sql dosyasını çalıştırın (OTP kullanacaksanız)
```

### 5. Web Server Konfigürasyonu

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Güvenlik
<Files ".env">
    Order allow,deny
    Deny from all
</Files>
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ /\.env {
    deny all;
}
```

## Ortam Bazlı Konfigürasyon

### Development Ortamı

```env
APP_ENV=development
APP_DEBUG=true
LOGIN_TYPE=phone
JWT_EXPIRATION_HOURS=24
```

### Staging Ortamı

```env
APP_ENV=staging
APP_DEBUG=false
LOGIN_TYPE=email
JWT_EXPIRATION_HOURS=12
```

### Production Ortamı

```env
APP_ENV=production
APP_DEBUG=false
LOGIN_TYPE=otp
JWT_EXPIRATION_HOURS=8
RATE_LIMIT_LOGIN_PER_IP_HOUR=10
```

## Güvenlik Kontrol Listesi

### ✅ Yapılması Gerekenler

- [ ] `.env` dosyasında güçlü şifreler kullanın
- [ ] JWT secret en az 32 karakter olsun
- [ ] Production'da `APP_DEBUG=false` olsun
- [ ] `.env` dosyası web'den erişilemez olsun
- [ ] HTTPS kullanın
- [ ] Veritabanı kullanıcısı sadece gerekli yetkilere sahip olsun
- [ ] SMS/Email API anahtarları güvenli olsun
- [ ] Rate limiting ayarlarını production'a uygun yapın

### ❌ Yapılmaması Gerekenler

- [ ] `.env` dosyasını git'e commit etmeyin
- [ ] Production'da test verilerini kullanmayın
- [ ] Varsayılan şifreleri değiştirmeden bırakmayın
- [ ] Debug mode'u production'da açık bırakmayın

## Müşteri Özelleştirme

Farklı müşteriler için sadece `.env` dosyasını değiştirmeniz yeterli:

### Müşteri A (Email Login)
```env
LOGIN_TYPE=email
LOGIN_REQUIRE_EMAIL_VERIFICATION=true
LOGIN_MIN_PASSWORD_LENGTH=8
UI_PRODUCT_CARD_COLOR="#ff6b6b"
```

### Müşteri B (OTP Login)
```env
LOGIN_TYPE=otp
OTP_CODE_LENGTH=6
OTP_EXPIRATION_MINUTES=3
UI_PRODUCT_CARD_COLOR="#4ecdc4"
```

## Sorun Giderme

### .env Dosyası Okunmuyor
- Dosya yolunu kontrol edin
- Dosya izinlerini kontrol edin (644)
- PHP'nin dosyayı okuma yetkisi olduğundan emin olun

### JWT Token Hataları
- JWT_SECRET değerinin doğru olduğundan emin olun
- Token süresini kontrol edin
- Server saatinin doğru olduğundan emin olun

### SMS/Email Gönderilmiyor
- API anahtarlarını kontrol edin
- Rate limiting ayarlarını kontrol edin
- Provider ayarlarını kontrol edin

## Monitoring

Production'da izlenmesi gereken metrikler:

- API response time'ları
- Başarısız login denemeleri
- SMS/Email gönderim oranları
- Database connection pool
- Error log'ları

## Backup

Düzenli olarak yedeklenmesi gerekenler:

- Veritabanı (günlük)
- `.env` dosyası (güvenli lokasyonda)
- Uygulama dosyaları (kod değişikliklerinde)
- Log dosyaları (haftalık)
