# Uygulama Konfigürasyon Rehberi

Bu dosya `app_config.php` dosyasının nasıl kullanılacağını ve farklı müşteriler için nasıl özelleştirileceğini açıklar.

## Genel Bakış

`app_config.php` dosyas<PERSON>, uygulamanın tüm ayarlarını tek yerden yönetmek için oluşturulmuştur. Bu sayede farklı müşteriler için özelleştirme yapmak çok daha kolay hale gelir.

## Environment Variables (.env) Sistemi

Hassas bilgiler artık `.env` dosyalarında saklanır ve kod deposuna commit edilmez.

### .env Dosyası Kurulumu

1. `.env.example` dosyasını `.env` olarak kopyalayın
2. Hassas bilgileri gerçek değerlerle değiştirin
3. `.env` dosyasının `.gitignore`'da olduğundan emin olun

### Frontend ve Backend Konfigürasyon Ayrımı

Güvenlik nedeniyle, konfigürasyon verileri iki kategoriye ayrılmıştır:

### 1. Frontend Konfigürasyonu (`getFrontendConfig()`)
- Frontend'e gönderilen güvenli veriler
- UI ayarları, login tipi, kullanıcı deneyimi ile ilgili ayarlar
- `web_get_config_data()` endpoint'i ile frontend'e gönderilir
- `.env` dosyasından güvenli değerleri okur

### 2. Backend Konfigürasyonu (`getBackendConfig()`)
- Sadece backend'de kullanılan hassas veriler
- JWT secret, encryption key, API anahtarları, veritabanı bilgileri
- Frontend'e asla gönderilmez
- `.env` dosyasından hassas değerleri okur

## Login Tipleri

Uygulama 3 farklı login tipini destekler:

### 1. Phone Login (Telefon + Şifre)
```php
"type" => "phone"
```
- Kullanıcılar telefon numarası ve şifre ile giriş yapar
- Telefon doğrulama SMS ile yapılır
- Varsayılan yöntemdir

### 2. Email Login (Email + Şifre)
```php
"type" => "email"
```
- Kullanıcılar email adresi ve şifre ile giriş yapar
- Email doğrulama email ile yapılır
- Kurumsal müşteriler için uygundur

### 3. OTP Login (Telefon + Tek Kullanımlık Şifre)
```php
"type" => "otp"
```
- Kullanıcılar sadece telefon numarası girer
- Her girişte SMS ile tek kullanımlık şifre gönderilir
- En güvenli yöntemdir

## Güvenlik Özellikleri

### Frontend'e Gönderilen Güvenli Veriler
- UI ayarları (renkler, bileşen ID'leri)
- Login tipi (phone, email, otp)
- Minimum şifre uzunluğu
- OTP kod uzunluğu ve süreleri
- Sosyal medya login seçenekleri
- Uygulama adı ve versiyonu

### Frontend'e Gönderilmeyen Hassas Veriler
- JWT secret key
- Encryption key
- SMS API anahtarları
- Email SMTP şifreleri
- Veritabanı bağlantı bilgileri
- Rate limiting detayları
- Hesap kilitleme süreleri
- OTP blok süreleri

## Müşteri Özelleştirme Örnekleri

### Örnek 1: Kurumsal Müşteri (Email Login)
`.env` dosyasında:
```env
LOGIN_TYPE=email
LOGIN_REQUIRE_EMAIL_VERIFICATION=true
LOGIN_REQUIRE_PHONE_VERIFICATION=false
LOGIN_MIN_PASSWORD_LENGTH=8
LOGIN_ALLOW_SOCIAL=false
EMAIL_VERIFICATION_REQUIRED=true
```

### Örnek 2: Güvenlik Odaklı Müşteri (OTP Login)
`.env` dosyasında:
```env
LOGIN_TYPE=otp
LOGIN_MAX_ATTEMPTS=3
LOGIN_LOCKOUT_DURATION_MINUTES=60
OTP_CODE_LENGTH=6
OTP_EXPIRATION_MINUTES=3
OTP_MAX_ATTEMPTS=3
OTP_BLOCK_DURATION_MINUTES=120
```

### Örnek 3: Standart E-ticaret (Phone Login)
`.env` dosyasında:
```env
LOGIN_TYPE=phone
LOGIN_REQUIRE_PHONE_VERIFICATION=true
LOGIN_ALLOW_SOCIAL=true
PHONE_ALLOWED_COUNTRY_CODES="+90"
PHONE_MIN_LENGTH=10
PHONE_MAX_LENGTH=11
```

## UI Özelleştirme

UI renklerini ve bileşenlerini özelleştirmek için:

```php
public static function getUIConfig()
{
    return [
        "productCardColor" => "#ffffff",
        "productCardSecondaryColor" => "#f8f9fa",
        "productCardBorderColor" => "#dee2e6",
        "headerid" => 1, // Farklı header tasarımı
        "categorycardid" => 2 // Farklı kategori kartı tasarımı
    ];
}
```

## SMS ve Email Ayarları

### SMS Konfigürasyonu
```php
public static function getSMSConfig()
{
    return [
        "provider" => "netgsm",
        "sender_name" => "FIRMAADI",
        "templates" => [
            "verification" => "Doğrulama kodunuz: {code}",
            "otp_login" => "Giriş kodunuz: {code}"
        ]
    ];
}
```

### Email Konfigürasyonu
```php
public static function getEmailConfig()
{
    return [
        "from_email" => "<EMAIL>",
        "from_name" => "Firma Adı",
        "templates" => [
            "verification" => [
                "subject" => "Email Doğrulama",
                "template" => "custom_verification_template"
            ]
        ]
    ];
}
```

## Güvenlik Ayarları

```php
public static function getSecurityConfig()
{
    return [
        "jwt_expiration_hours" => 24, // Token süresi
        "refresh_token_expiration_days" => 30, // Refresh token süresi
        "rate_limiting" => [
            "login_attempts_per_ip_per_hour" => 50,
            "api_requests_per_minute" => 200
        ]
    ];
}
```

## Kullanım Örnekleri

### Frontend'de Konfigürasyon Kullanımı
```javascript
// Frontend'de config verilerini almak için
fetch('/authentication.php', {
    method: 'POST',
    body: new FormData([['f', 'web_get_config_data']])
})
.then(response => response.json())
.then(config => {
    // Sadece güvenli veriler gelir
    console.log('Login tipi:', config.loginType.type);
    console.log('UI ayarları:', config.productCardColor);
    // JWT secret vs. burada olmaz!
});
```

### Backend'de Konfigürasyon Kullanımı
```php
// Backend'de hassas verilere erişim
$backendConfig = AppConfig::getBackendConfig();
$jwtSecret = $backendConfig['security']['jwt_secret'];
$smsApiKey = $backendConfig['sms']['api_key'];

// Sadece frontend verileri
$frontendConfig = AppConfig::getFrontendConfig();
$loginType = $frontendConfig['loginType']['type'];
```

## Deployment Rehberi

### 1. Geliştirme Ortamı
```php
public static function getAppSettings()
{
    return [
        'environment' => 'development',
        'debug_mode' => true
    ];
}
```

### 2. Production Ortamı
```php
public static function getAppSettings()
{
    return [
        'environment' => 'production',
        'debug_mode' => false
    ];
}
```

## API Endpoint'leri

Yeni login sistemi aşağıdaki endpoint'leri destekler:

### Authentication Endpoint'leri
- `POST /authentication.php` - `f=login_user` - Kullanıcı girişi
- `POST /authentication.php` - `f=register_user` - Kullanıcı kaydı
- `POST /authentication.php` - `f=logout_user` - Kullanıcı çıkışı

### OTP Endpoint'leri
- `POST /authentication.php` - `f=send_otp` - OTP kodu gönderme
- `POST /authentication.php` - `f=verify_otp_login` - OTP ile giriş

### Doğrulama Endpoint'leri
- `POST /authentication.php` - `f=verify_email` - Email doğrulama
- `POST /authentication.php` - `f=verify_phone` - Telefon doğrulama
- `POST /authentication.php` - `f=resend_verification` - Doğrulama kodu yeniden gönderme

### Frontend İstek Formatları

#### Email ile Kayıt
```javascript
POST /authentication.php
FormData: {
  f: "register_user",
  email: "<EMAIL>",
  password: "sifre123",
  registerType: "email"
}
```

#### Telefon ile Kayıt
```javascript
POST /authentication.php
FormData: {
  f: "register_user",
  phoneNumber: "(555) 123 45 67",
  password: "sifre123",
  registerType: "phone"
}
```

#### Email ile Giriş
```javascript
POST /authentication.php
FormData: {
  f: "login_user",
  email: "<EMAIL>",
  password: "sifre123",
  loginType: "email"
}
```

#### Telefon ile Giriş
```javascript
POST /authentication.php
FormData: {
  f: "login_user",
  phoneNumber: "(555) 123 45 67",
  password: "sifre123",
  loginType: "phone"
}
```

#### OTP Gönderme
```javascript
POST /authentication.php
FormData: {
  f: "send_otp",
  phoneNumber: "(555) 123 45 67"
}
```

#### OTP ile Giriş
```javascript
POST /authentication.php
FormData: {
  f: "verify_otp_login",
  phoneNumber: "(555) 123 45 67",
  otpCode: "123456"
}
```

## Sorun Giderme

### Login Çalışmıyor
1. `app_config.php` dosyasındaki login tipini kontrol edin
2. Veritabanında yeni alanların eklendiğinden emin olun
3. SMS/Email servislerinin çalıştığını kontrol edin

### Doğrulama Kodları Gelmiyor
1. SMS provider ayarlarını kontrol edin
2. Email SMTP ayarlarını kontrol edin
3. Rate limiting ayarlarını kontrol edin

### Token Hataları
1. JWT secret key'in doğru olduğundan emin olun
2. Token süresini kontrol edin
3. Refresh token mekanizmasını kontrol edin
