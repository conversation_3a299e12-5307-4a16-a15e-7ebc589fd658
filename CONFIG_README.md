# Uygulama Konfigürasyon Rehberi

Bu dosya `app_config.php` dosyasının nasıl kullanılacağını ve farklı müşteriler için nasıl özelleştirileceğini açıklar.

## Genel Bakış

`app_config.php` dosyas<PERSON>, uygulamanın tüm ayarlarını tek yerden yönetmek için oluşturulmuştur. Bu sayede farklı müşteriler için özelleştirme yapmak çok daha kolay hale gelir.

## Login Tipleri

Uygulama 3 farklı login tipini destekler:

### 1. Phone Login (Telefon + Şifre)
```php
"type" => "phone"
```
- Kullanıcılar telefon numarası ve şifre ile giriş yapar
- Telefon doğrulama SMS ile yapılır
- Varsayılan yöntemdir

### 2. <PERSON><PERSON> (Email + Şifre)
```php
"type" => "email"
```
- <PERSON>llanı<PERSON>ılar email adresi ve şifre ile giriş yapar
- <PERSON><PERSON> doğ<PERSON>a email ile yapılır
- Kurumsal müşteriler için uygundur

### 3. OTP Login (Telefon + Tek Kullanımlık Şifre)
```php
"type" => "otp"
```
- Kullanıcılar sadece telefon numarası girer
- Her girişte SMS ile tek kullanımlık şifre gönderilir
- En güvenli yöntemdir

## Müşteri Özelleştirme Örnekleri

### Örnek 1: Kurumsal Müşteri (Email Login)
```php
public static function getLoginConfig()
{
    return [
        "type" => "email",
        "requireEmailVerification" => true,
        "requirePhoneVerification" => false,
        "minPasswordLength" => 8,
        "allowSocialLogin" => false,
        "email" => [
            "verificationRequired" => true,
            "allowedDomains" => ["company.com", "subsidiary.com"],
            "blockedDomains" => []
        ]
    ];
}
```

### Örnek 2: Güvenlik Odaklı Müşteri (OTP Login)
```php
public static function getLoginConfig()
{
    return [
        "type" => "otp",
        "maxLoginAttempts" => 3,
        "lockoutDurationMinutes" => 60,
        "otp" => [
            "codeLength" => 6,
            "expirationMinutes" => 3,
            "maxAttempts" => 3,
            "blockDurationMinutes" => 120
        ]
    ];
}
```

### Örnek 3: Standart E-ticaret (Phone Login)
```php
public static function getLoginConfig()
{
    return [
        "type" => "phone",
        "requirePhoneVerification" => true,
        "allowSocialLogin" => true,
        "socialProviders" => ["google", "facebook"],
        "phone" => [
            "allowedCountryCodes" => ["+90"],
            "minLength" => 10,
            "maxLength" => 11
        ]
    ];
}
```

## UI Özelleştirme

UI renklerini ve bileşenlerini özelleştirmek için:

```php
public static function getUIConfig()
{
    return [
        "productCardColor" => "#ffffff",
        "productCardSecondaryColor" => "#f8f9fa",
        "productCardBorderColor" => "#dee2e6",
        "headerid" => 1, // Farklı header tasarımı
        "categorycardid" => 2 // Farklı kategori kartı tasarımı
    ];
}
```

## SMS ve Email Ayarları

### SMS Konfigürasyonu
```php
public static function getSMSConfig()
{
    return [
        "provider" => "netgsm",
        "sender_name" => "FIRMAADI",
        "templates" => [
            "verification" => "Doğrulama kodunuz: {code}",
            "otp_login" => "Giriş kodunuz: {code}"
        ]
    ];
}
```

### Email Konfigürasyonu
```php
public static function getEmailConfig()
{
    return [
        "from_email" => "<EMAIL>",
        "from_name" => "Firma Adı",
        "templates" => [
            "verification" => [
                "subject" => "Email Doğrulama",
                "template" => "custom_verification_template"
            ]
        ]
    ];
}
```

## Güvenlik Ayarları

```php
public static function getSecurityConfig()
{
    return [
        "jwt_expiration_hours" => 24, // Token süresi
        "refresh_token_expiration_days" => 30, // Refresh token süresi
        "rate_limiting" => [
            "login_attempts_per_ip_per_hour" => 50,
            "api_requests_per_minute" => 200
        ]
    ];
}
```

## Deployment Rehberi

### 1. Geliştirme Ortamı
```php
public static function getAppSettings()
{
    return [
        'environment' => 'development',
        'debug_mode' => true
    ];
}
```

### 2. Production Ortamı
```php
public static function getAppSettings()
{
    return [
        'environment' => 'production',
        'debug_mode' => false
    ];
}
```

## API Endpoint'leri

Yeni login sistemi aşağıdaki endpoint'leri destekler:

### Authentication Endpoint'leri
- `POST /authentication.php` - `f=login_user` - Kullanıcı girişi
- `POST /authentication.php` - `f=register_user` - Kullanıcı kaydı
- `POST /authentication.php` - `f=logout_user` - Kullanıcı çıkışı

### OTP Endpoint'leri
- `POST /authentication.php` - `f=send_otp` - OTP kodu gönderme
- `POST /authentication.php` - `f=verify_otp_login` - OTP ile giriş

### Doğrulama Endpoint'leri
- `POST /authentication.php` - `f=verify_email` - Email doğrulama
- `POST /authentication.php` - `f=verify_phone` - Telefon doğrulama
- `POST /authentication.php` - `f=resend_verification` - Doğrulama kodu yeniden gönderme

### Frontend İstek Formatları

#### Email ile Kayıt
```javascript
POST /authentication.php
FormData: {
  f: "register_user",
  email: "<EMAIL>",
  password: "sifre123",
  registerType: "email"
}
```

#### Telefon ile Kayıt
```javascript
POST /authentication.php
FormData: {
  f: "register_user",
  phoneNumber: "(555) 123 45 67",
  password: "sifre123",
  registerType: "phone"
}
```

#### Email ile Giriş
```javascript
POST /authentication.php
FormData: {
  f: "login_user",
  email: "<EMAIL>",
  password: "sifre123",
  loginType: "email"
}
```

#### Telefon ile Giriş
```javascript
POST /authentication.php
FormData: {
  f: "login_user",
  phoneNumber: "(555) 123 45 67",
  password: "sifre123",
  loginType: "phone"
}
```

#### OTP Gönderme
```javascript
POST /authentication.php
FormData: {
  f: "send_otp",
  phoneNumber: "(555) 123 45 67"
}
```

#### OTP ile Giriş
```javascript
POST /authentication.php
FormData: {
  f: "verify_otp_login",
  phoneNumber: "(555) 123 45 67",
  otpCode: "123456"
}
```

## Sorun Giderme

### Login Çalışmıyor
1. `app_config.php` dosyasındaki login tipini kontrol edin
2. Veritabanında yeni alanların eklendiğinden emin olun
3. SMS/Email servislerinin çalıştığını kontrol edin

### Doğrulama Kodları Gelmiyor
1. SMS provider ayarlarını kontrol edin
2. Email SMTP ayarlarını kontrol edin
3. Rate limiting ayarlarını kontrol edin

### Token Hataları
1. JWT secret key'in doğru olduğundan emin olun
2. Token süresini kontrol edin
3. Refresh token mekanizmasını kontrol edin
