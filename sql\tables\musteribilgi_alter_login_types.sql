-- Musteri<PERSON>gi tablosunu login tiplerini destekleyecek şekilde güncelleme
-- Bu dosya farklı login tiplerini (otp, phone, email) desteklemek için gerekli alanları ekler

-- Email doğrulama için alanlar
ALTER TABLE `musteribilgi` 
ADD COLUMN `email_verified` TINYINT(1) DEFAULT 0 COMMENT 'Email doğrulandı mı?',
ADD COLUMN `email_verification_code` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Email doğrulama kodu',
ADD COLUMN `email_verification_time` DATETIME DEFAULT NULL COMMENT 'Email doğrulama kodu gönderilme zamanı';

-- Telefon doğrulama için alanlar (mevcut verification_code alanı telefon i<PERSON><PERSON>)
ALTER TABLE `musteribilgi` 
ADD COLUMN `phone_verified` TINYINT(1) DEFAULT 0 COMMENT 'Telefon doğrulandı mı?';

-- OTP (One Time Password) için alanlar
ALTER TABLE `musteribilgi` 
ADD COLUMN `otp_code` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'OTP kodu',
ADD COLUMN `otp_code_time` DATETIME DEFAULT NULL COMMENT 'OTP kodu gönderilme zamanı',
ADD COLUMN `otp_attempts` INT DEFAULT 0 COMMENT 'OTP deneme sayısı',
ADD COLUMN `otp_blocked_until` DATETIME DEFAULT NULL COMMENT 'OTP bloklanma süresi';

-- Login tipi ve tercih edilen login yöntemi
ALTER TABLE `musteribilgi` 
ADD COLUMN `preferred_login_type` ENUM('phone', 'email', 'otp') DEFAULT 'phone' COMMENT 'Tercih edilen login tipi',
ADD COLUMN `login_method_used` ENUM('phone', 'email', 'otp', 'social') DEFAULT 'phone' COMMENT 'Son kullanılan login yöntemi';

-- Güvenlik için ek alanlar
ALTER TABLE `musteribilgi` 
ADD COLUMN `failed_login_attempts` INT DEFAULT 0 COMMENT 'Başarısız login deneme sayısı',
ADD COLUMN `account_locked_until` DATETIME DEFAULT NULL COMMENT 'Hesap kilitleme süresi',
ADD COLUMN `last_otp_request` DATETIME DEFAULT NULL COMMENT 'Son OTP talep zamanı';

-- İndeksler ekleme
ALTER TABLE `musteribilgi` 
ADD KEY `idx_email_verified` (`email_verified`),
ADD KEY `idx_phone_verified` (`phone_verified`),
ADD KEY `idx_email_verification_code` (`email_verification_code`),
ADD KEY `idx_otp_code` (`otp_code`),
ADD KEY `idx_preferred_login_type` (`preferred_login_type`),
ADD KEY `idx_account_locked_until` (`account_locked_until`);

-- Mail alanını daha büyük yapalım (sosyal medya email'leri uzun olabilir)
ALTER TABLE `musteribilgi` 
MODIFY COLUMN `mail` VARCHAR(320) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL;
