<?php

use Nullix\CryptoJsAes\CryptoJsAes;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

require "js/aes/CryptoJsAes.php";
//include edilen fonksiyon.php deki bazı fonksiyon adları aynı onları exclude edebilmek için
//bu değişenden faydalanacağız.
$istektipi = "web";
cors();
include_once('fonksiyon.php');
include_once('app_config.php');
function cors()
{
    // Allow from any origin
    if (isset($_SERVER['HTTP_ORIGIN'])) {
        $allowed_origins = [
            'http://localhost:3000',
            'http://localhost:5000',
        ];
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        if (preg_match('/^https:\/\/.*\.replit\.dev$/', $origin)) {
            $allowed_origins[] = $origin;
        }
        if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'], $allowed_origins)) {
            header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
            header("Access-Control-Allow-Credentials: true");
        }
        header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');    // cache for 1 day
    }
    // Access-Control headers are received during OPTIONS requests
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
            // may also be using PUT, PATCH, HEAD etc
            header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        }
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
            header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        }
        exit(0);
    }
}
//cors();
function decrypt_web_aes($encrypted)
{
    //$encrypted = "{\"ct\":\"+iGqH7OSW/FgLs8dvVCmKg==\",\"iv\":\"89e56bd1b21ed042def1ca62a8f46b6d\",\"s\":\"9f7613448b65fef7\"}";
    $password = "123456";
    $decrypted = CryptoJsAes::decrypt($encrypted, $password);
    return $decrypted;
}
$validFunctions = array(
    "get_urunler",
    "get_and_save_aranan_kelime",
    "get_gecmis_arama",
    "get_altgruplar",
    "geri_bildirimler",
    "get_vitrin_data",
    "web_add_new_user",
    "web_login_app_user",
    "web_change_password",
    "web_get_init_data",
    "web_get_config_data",
    "get_gruplar",
    "get_subeler_data",
    "complete_aranan_kelime",
    "get_kampanyalar_data",
    "get_product_detail"
);
$authorizedEndpoints = array(
    "get_cart",
    "update_cart",
    "get_musteriadres",
);
if (in_array($functName, $validFunctions)) {
    if ($functName == 'get_urunler') {
        $functName(
            $_POST['grup'],
            $_POST['altgrup'],
            $_POST['depo']
        );
    } else  if ($functName == 'get_and_save_aranan_kelime') {
        $functName(
            $_POST['aratilan_kelime'],
            $_POST['depo'],
            $_POST['user'],
            $_POST['uid'],
            $_POST['os']
        );
    } else  if ($functName == 'complete_aranan_kelime') {
        $functName(
            $_POST['aratilan_kelime'],
            $_POST['depo']
        );
    } else  if ($functName == 'get_gecmis_arama') {
        $functName(
            $_POST['user'],
            $_POST['uid']
        );
    } else  if ($functName == 'get_altgruplar') {
        $functName(
            $_POST['grup'],
            $_POST['deposahip']
        );
    } else  if ($functName == 'get_gruplar') {
        $functName(
            $_POST['deposahip']
        );
    } else  if ($functName == 'geri_bildirimler') {
        $functName(
            $_POST['user'],
            $_POST['uid'],
            $_POST['baslik'],
            $_POST['icerik'],
            $_POST['adsoyad'],
            $_POST['email'],
            $_POST['telefon'],
        );
    } else if ($functName == 'get_vitrin_data') {
        $functName(
            $_POST['deposahip'],
            $_POST['depo']
        );
    } else if ($functName == 'get_kampanyalar_data') {
        $functName(
            $_POST['deposahip'],
            $_POST['depo']
        );
    } elseif ($functName == 'web_add_new_user') {
        $functName(
            $_POST['ceptel'],
            $_POST['adsoyad'],
            $_POST['mail'],
            $_POST['sifre'],
            $_POST['os'],
            $_POST['fb']
        );
    } elseif ($functName == 'web_login_app_user') {
        $functName(
            $_POST['ceptel'],
            $_POST['sifre']
        );
    } elseif ($functName == 'get_product_detail') {
        $functName(
            $_POST['id'],
            $_POST['slug']
        );
    } else if ($functName == 'web_change_password') {
        $functName(
            $_POST['user'],
            $_POST['expassword'],
            $_POST['newpassword'],
            $_POST['uid'],
            $_POST['forgotpassword']
        );
        // $functName( $_POST['tutar'],$_POST['ad'],$_POST['tcno'],$_POST['banka']);
    } else if ($functName == 'web_get_init_data') {
        if (isset($_POST["user"]) && isset($_POST["uid"])) {
            $functName(
                $_POST['user'],
                $_POST['uid'],
                $_POST['depo'],
                $_POST['os'],
                $_POST['version'],
                $_POST['fbid'],
                $_POST['teslimattip']
            );
        } else {
            $functName(
                "",
                "",
                $_POST['depo'],
                $_POST['os'],
                $_POST['version'],
                $_POST['fbid'],
                $_POST['teslimattip']
            );
        }
        // $functName( $_POST['tutar'],$_POST['ad'],$_POST['tcno'],$_POST['banka']);
    } else  if ($functName == 'get_subeler_data') {
        $functName(
            $_POST['deposahip']
        );
    } else  if ($functName == 'web_get_config_data') {
        $functName();
    }
} else if (in_array($functName, $authorizedEndpoints)) {
    $userid = authenticate_user();
    if ($userid) {
        // $userPermissions = getUserPermissions($userid);
        // error_log("user permissions array  ========>>>         " . json_encode($userPermissions));
        if ($functName == 'get_cart') {
            $functName(
                $userid
            );
        } else  if ($functName == 'update_cart') {
            $functName(
                $userid,
                $_POST['product_id'],
                $_POST['quantity'],
                $_POST['price']
            );
        } else  if ($functName == 'get_musteriadres') {
            $functName(
                $userid
            );
        }
    } else {
        $response = new ErrorResult('Unauthorized Access');
        $response->send(401);
    }
}
function get_musteriadres($userid)
{
    global $link;
    $arr = array();
    //------------------ GEÇMİŞ ADRESLER --------------------
    // Sorguyu hazırlama
    $call = mysqli_prepare($link, 'SELECT * FROM musteriadres WHERE iptalmi=0 AND musteri = ?');
    if (!$call) {
        $verify_status = new ErrorResult("Failed to prepare query: " . mysqli_error($link));
        $verify_status->send(500);
        return;
    }
    // Parametreyi bağlama
    mysqli_stmt_bind_param($call, 's', $userid);
    // Sorguyu çalıştırma
    if (!mysqli_stmt_execute($call)) {
        $verify_status = new ErrorResult("Query execution failed: " . mysqli_stmt_error($call));
        $verify_status->send(500);
        return;
    }
    // Sonuçları al
    $rs = mysqli_stmt_get_result($call);
    if (!$rs) {
        $verify_status = new ErrorResult("Failed to get results: " . mysqli_stmt_error($call));
        $verify_status->send(500);
        return;
    }
    // Sonuç var mı kontrol et
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    // Adresler bulunmasa bile başarılı sonuç dön
    $verify_status = new SuccessResult($arr);
    $verify_status->send(200);
}
function get_urunler($grup, $altgrup, $depo)
{
    if (!isset($depo) || $depo == "") {
        $depo = 1;
    }
    if (!isset($altgrup)) {
        $altgrup = -1;
    }
    global $link;
    if ($depo == 31) {
        $depo = 1;
    }
    error_log("fff f");
    $query = "SELECT id, ad, slug, aciklama, grup, altgrup,
    ROUND(satisfiyat, 2) AS satisfiyat,
    LEAST(GREATEST(LEAST(adet, IFNULL(siparislimit, 1000)), sabitstok), IFNULL(siparislimit, 1000)) AS adet,
    kdvoran, imageupdate, kampanyasizsatisfiyat, kampanyalimi, barkod, stok_kodu AS stokkod,
    birim, (SELECT IFNULL(minimumadet, 0)) AS mincount
    FROM stok
    WHERE aktifmi = 1 AND flagweb = 1
    AND (adet > 2 OR sabitstok > 0)
    AND satisfiyat > 0 AND barkod != ''
    AND depo = $depo AND grup = $grup
    AND flagweb = 1";
    if ($altgrup != -1) {
        $query .= " AND altgrup = $altgrup";
    }
    $query .= " ORDER BY sirala ASC, ad ASC";
    if ($grup == 1000) {
        $query = "SELECT id, ad, slug, aciklama, grup, altgrup,
        ROUND(satisfiyat, 2) AS satisfiyat,
        LEAST(GREATEST(LEAST(adet, IFNULL(siparislimit, 1000)), sabitstok), IFNULL(siparislimit, 1000)) AS adet,
        kdvoran, imageupdate, kampanyasizsatisfiyat, kampanyalimi, barkod, stok_kodu AS stokkod,
        birim, (SELECT IFNULL(minimumadet, 0)) AS mincount
        FROM stok
        WHERE aktifmi = 1 AND flagweb = 1
        AND (adet > 2 OR sabitstok > 0)
        AND satisfiyat > 0 AND barkod != ''
        AND depo = $depo AND kampanyalimi = 1
        AND flagweb = 1
        ORDER BY sirala ASC, ad ASC";
    }
    $rs = mysqli_query($link, $query);
    $arr = array();
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    // Grup bilgisi ekleme
    $groupInfo = array();
    if (!empty($arr)) {
        $grupQuery = "SELECT id, ad FROM grup WHERE id = $grup";
        $grupResult = mysqli_query($link, $grupQuery);
        $grupData = mysqli_fetch_object($grupResult);
        if ($grupData) {
            $groupInfo["grup"] = ["id" => $grupData->id, "ad" => $grupData->ad];
        }
        if ($altgrup != -1) {
            $altgrupQuery = "SELECT id, ad FROM altgrup WHERE id = $altgrup";
            $altgrupResult = mysqli_query($link, $altgrupQuery);
            $altgrupData = mysqli_fetch_object($altgrupResult);
            if ($altgrupData) {
                $groupInfo["altgrup"] = ["id" => $altgrupData->id, "ad" => $altgrupData->ad];
            }
        }
    }
    $response = array_merge(["urunler" => $arr], $groupInfo);
    echo json_encode($response);
}
function get_product_detail($id, $slug)
{
    global $link;
    $query = "SELECT
                id,
                ad,
                slug,
                aciklama,
                grup,
                altgrup,
                ROUND(satisfiyat, 2) AS satisfiyat,
                LEAST(GREATEST(LEAST(adet, IFNULL(siparislimit, 1000)), sabitstok), IFNULL(siparislimit, 1000)) AS adet,
                kdvoran,
                imageupdate,
                kampanyasizsatisfiyat,
                kampanyalimi,
                barkod,
                stok_kodu AS stokkod,
                birim,
                (SELECT IFNULL(minimumadet, 0)) AS mincount
              FROM stok
              WHERE id = $id
                AND aktifmi = 1";
    $rs = mysqli_query($link, $query);
    if ($obj = mysqli_fetch_object($rs)) {
        if ($obj->slug === $slug) {
            $urun = array(
                'id' => $obj->id,
                'ad' => $obj->ad,
                'slug' => $obj->slug,
                'aciklama' => $obj->aciklama,
                'grup' => $obj->grup,
                'altgrup' => $obj->altgrup,
                'satisfiyat' => $obj->satisfiyat,
                'adet' => $obj->adet,
                'kdvoran' => $obj->kdvoran,
                'imageupdate' => $obj->imageupdate,
                'kampanyasizsatisfiyat' => $obj->kampanyasizsatisfiyat,
                'kampanyalimi' => $obj->kampanyalimi,
                'barkod' => $obj->barkod,
                'stokkod' => $obj->stokkod,
                'birim' => $obj->birim,
                'mincount' => $obj->mincount
            );
            echo json_encode(array("output" => $urun));
        } else {
            echo json_encode(array("error" => "Slug eşleşmiyor."));
        }
    } else {
        echo json_encode(array("error" => "Ürün bulunamadı."));
    }
}
function complete_aranan_kelime($aratilan_kelime, $depo)
{
    $cap_kelime = Transliterator::create("tr-Upper")
        ->transliterate($aratilan_kelime);
    // $cap_kelime = strtoupper($aratilan_kelime);
    if (!isset($depo) || $depo == 0 || $depo == -1 || $depo == "")
        $depo = 1;
    global $link;
    //error_log($cap_kelime,0);
    $query = "(select id,ad,slug,aciklama,grup,altgrup,ROUND (satisfiyat, 2) as satisfiyat,
    least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, kdvoran,imageupdate,
    markaoncelik,kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
    birim,grupad,altgrupad from stokview where  (ad like '% $aratilan_kelime %' or ad like  '% $cap_kelime %' ) and aktifmi = 1 and flagweb=1 and (adet > 2 or sabitstok > 0)
    and satisfiyat > 0 and barkod != '' and depo=$depo  and grup != -1 and altgrup != -1 and grup != 0 and altgrup != 0  and sabitstok != -1 and altgrupad is not null
    order by sirala asc, markaoncelik desc, ad asc)
    union
    (select id,ad,slug,aciklama,grup,altgrup,ROUND (satisfiyat, 2) as satisfiyat,
    least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, kdvoran,imageupdate,
    markaoncelik,kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
    birim,grupad,altgrupad from stokview where  (ad like '%$aratilan_kelime %' or ad like  '%$cap_kelime %' ) and aktifmi = 1 and flagweb=1 and (adet > 2 or sabitstok > 0)
    and satisfiyat > 0 and barkod != '' and depo=$depo  and grup != -1 and altgrup != -1 and grup != 0 and altgrup != 0  and sabitstok != -1 and altgrupad is not null
    order by sirala asc, markaoncelik desc, ad asc)
    union
    (select id,ad,slug,aciklama,grup,altgrup,ROUND (satisfiyat, 2) as satisfiyat,
    least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, kdvoran,imageupdate,
    markaoncelik,kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
    birim,grupad,altgrupad from stokview where  (ad like '% $aratilan_kelime%' or ad like  '% $cap_kelime%' ) and aktifmi = 1 and flagweb=1 and (adet > 2 or sabitstok > 0)
    and satisfiyat > 0 and barkod != '' and depo=$depo  and grup != -1 and altgrup != -1 and grup != 0 and altgrup != 0  and sabitstok != -1 and altgrupad is not null
    order by sirala asc, markaoncelik desc, ad asc)
    union
    (select id,ad,slug,aciklama,grup,altgrup,ROUND (satisfiyat, 2) as satisfiyat,
    least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, kdvoran,imageupdate,
    markaoncelik,kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
    birim,grupad,altgrupad from stokview where  (ad like '%$aratilan_kelime%' or ad like  '%$cap_kelime%' ) and aktifmi = 1 and flagweb=1 and (adet > 2 or sabitstok > 0)
    and satisfiyat > 0 and barkod != '' and depo=$depo  and grup != -1 and altgrup != -1 and grup != 0 and altgrup != 0  and sabitstok != -1 and altgrupad is not null
    order by sirala asc, markaoncelik desc, ad asc)
    ";
    // $query = "SELECT * FROM stok where ad LIKE '%$aratilan_kelime%' and aktifmi = 1 and (adet > 2 or sabitstok > 0)
    // and satisfiyat > 0 and barkod != '' and depo='$depo' ORDER BY `stok`.`ad` ASC";
    $call = mysqli_prepare($link, $query);
    // echo $query;
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);
    if ($rs) {
        while ($obj = mysqli_fetch_object($rs)) {
            $arr[] = $obj;
        }
        //     $last_ar = array();
        //     $ar2 = array("sonuc" => $arr);
        //     array_push($last_ar, $ar2);
        $verify_status = array("status" => "success", "response" => $arr);
    } else {
        $verify_status = array("status" => "fail");
    }
    echo json_encode($verify_status);
}
function get_and_save_aranan_kelime($aratilan_kelime, $depo, $user, $uid, $os)
{
    global $link;
    $date = date("Y-m-d H:i:s");
    $last_ar = array();
    $arr = array();
    $cap_kelime = Transliterator::create("tr-Upper")
        ->transliterate($aratilan_kelime);
    $call = mysqli_prepare($link, 'select id from musteribilgi where tel=? and uid=?');
    mysqli_stmt_bind_param($call, 'ss', $user, $uid);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);
    $row_count = mysqli_num_rows($rs);
    if ($row_count < 1) {
        // return;
        echo "";
    } else {
        $row = mysqli_fetch_row($rs);
    }
    $call = mysqli_prepare($link, 'insert into gecmis_aramalar (kelime, musteri, tarih, os) values (?,?,?,?)');
    mysqli_stmt_bind_param($call, 'siss', $aratilan_kelime, $row[0], $date, $os);
    (mysqli_stmt_execute($call));
    // echo $aratilan_kelime;
    $query = "(select id,ad,slug,aciklama,grup,altgrup,ROUND (satisfiyat, 2) as satisfiyat,
    least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, kdvoran,imageupdate,
    markaoncelik,kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
    birim,grupad,altgrupad, (select ifnull(minimumadet, 0)) as mincount from stokview where  (ad like '% $aratilan_kelime %' or ad like  '% $cap_kelime %' ) and aktifmi = 1 and flagweb=1 and (adet > 2 or sabitstok > 0)
    and satisfiyat > 0 and barkod != '' and depo=$depo  and grup != -1 and altgrup != -1 and grup != 0 and altgrup != 0  and sabitstok != -1
    order by sirala asc, markaoncelik desc, ad asc)
    union
    (select id,ad,slug,aciklama,grup,altgrup,ROUND (satisfiyat, 2) as satisfiyat,
    least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, kdvoran,imageupdate,
    markaoncelik,kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
    birim,grupad,altgrupad, (select ifnull(minimumadet, 0)) as mincount from stokview where  (ad like '%$aratilan_kelime %' or ad like  '%$cap_kelime %' ) and aktifmi = 1 and flagweb=1 and (adet > 2 or sabitstok > 0)
    and satisfiyat > 0 and barkod != '' and depo=$depo  and grup != -1 and altgrup != -1 and grup != 0 and altgrup != 0  and sabitstok != -1
    order by sirala asc, markaoncelik desc, ad asc)
    union
    (select id,ad,slug,aciklama,grup,altgrup,ROUND (satisfiyat, 2) as satisfiyat,
    least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, kdvoran,imageupdate,
    markaoncelik,kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
    birim,grupad,altgrupad, (select ifnull(minimumadet, 0)) as mincount from stokview where  (ad like '% $aratilan_kelime%' or ad like  '% $cap_kelime%' ) and aktifmi = 1 and flagweb=1 and (adet > 2 or sabitstok > 0)
    and satisfiyat > 0 and barkod != '' and depo=$depo  and grup != -1 and altgrup != -1 and grup != 0 and altgrup != 0  and sabitstok != -1
    order by sirala asc, markaoncelik desc, ad asc)
    union
    (select id,ad,slug,aciklama,grup,altgrup,ROUND (satisfiyat, 2) as satisfiyat,
    least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, kdvoran,imageupdate,
    markaoncelik,kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
    birim,grupad,altgrupad, (select ifnull(minimumadet, 0)) as mincount from stokview where  (ad like '%$aratilan_kelime%' or ad like  '%$cap_kelime%' ) and aktifmi = 1 and flagweb=1 and (adet > 2 or sabitstok > 0)
    and satisfiyat > 0 and barkod != '' and depo=$depo  and grup != -1 and altgrup != -1 and grup != 0 and altgrup != 0  and sabitstok != -1
    order by sirala asc, markaoncelik desc, ad asc)
    ";
    // $query = "SELECT * FROM stok where ad LIKE '%$aratilan_kelime%' and aktifmi = 1 and (adet > 2 or sabitstok > 0)
    // and satisfiyat > 0 and barkod != '' and depo='$depo' ORDER BY `stok`.`ad` ASC";
    $call = mysqli_prepare($link, $query);
    // echo $query;
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);
    if ($rs) {
        while ($obj = mysqli_fetch_object($rs)) {
            $arr[] = $obj;
        }
        $ar2 = array("aramalar" => $arr);
        array_push($last_ar, $ar2);
        $verify_status = array("status" => "success", "response" => $last_ar);
    } else {
        $verify_status = array("status" => "fail");
    }
    echo json_encode($verify_status);
    // $a =  json_encode($verify_status, true);
    // print_r ($a);
}
function get_gecmis_arama($user, $uid)
{
    global $link;
    $arr = array();
    $last_ar = array();
    $call = mysqli_prepare($link, 'SELECT DISTINCT kelime,id FROM gecmis_aramalar where musteri = (select id from musteribilgi where tel=? and uid=?)
	ORDER BY id DESC LIMIT 5');
    mysqli_stmt_bind_param($call, 'ss', $user, $uid);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);
    if ($rs) {
        while ($obj = mysqli_fetch_object($rs)) {
            $arr[] = $obj;
        }
        $ar2 = array("gecmis_aramalar" => $arr);
        array_push($last_ar, $ar2);
        $verify_status = array("status" => "success", "response" => $last_ar);
    } else {
        $verify_status = array("status" => "fail", "detail" => mysqli_error($link));
    }
    echo json_encode($verify_status);
}
function get_altgruplar($grup, $deposahip)
{
    global $link;
    $arr = array();
    //$rs = mysqli_query($link, "select * from altgrup order by sirala asc");
    //boş altgruplar gorunmesin diye yaptık
    $rs = mysqli_query($link, "select * from altgrup where id in (select altgrup from stok where (adet > 2 or sabitstok > 0 ) and aktifmi = 1 and deposahip = $deposahip and grup=$grup) and deposahip = $deposahip and grup=$grup order by ad asc");
    $first_object = false;
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    if ($grup == 1000) {
        $obj_org = array();
        $obj_org["id"] = "1000";
        $obj_org["grup"] = "1000";
        $obj_org["ad"] = "İndirimli Ürünler";
        $obj_org["indexid"] = "1000";
        $arr[] = $obj_org;
    }
    $ar2 = array("altgruplar" => $arr);
    echo json_encode($ar2);
    //  array_push($last_ar, $ar2);
}
function get_gruplar($deposahip)
{
    //  error_log("test",0);
    global $link;
    $altgruplar = array();
    //$rs = mysqli_query($link, "select * from altgrup order by sirala asc");
    //boş altgruplar gorunmesin diye yaptık
    $rs = mysqli_query($link, "select * from altgrup where id in (select altgrup from stok where (adet > 2 or sabitstok > 0 ) and aktifmi = 1 and deposahip = $deposahip ) and deposahip = $deposahip  order by ad asc");
    $first_object = false;
    while ($obj = mysqli_fetch_assoc($rs)) {
        $altgruplar[$obj['grup']][] = $obj;
    }
    $arr = array();
    //$rs = mysqli_query($link, "select * from altgrup order by sirala asc");
    //boş altgruplar gorunmesin diye yaptık
    $rs = mysqli_query($link, "select * from grup where  aktifmi = 1 and deposahip = $deposahip   order by sirala asc");
    $first_object = false;
    while ($obj = mysqli_fetch_object($rs)) {
        if (!$first_object) {
            //eger indirimli urun hiç yoksa o grubu eklemeyelim
            //burada depo parametresi yok o yuzden depo değişkenini 1 yaptık.
            $call = mysqli_prepare($link, "select count(id) from stok where deposahip = ? and aktifmi=1 and flagweb = 1 and  depo=1 and (adet > 2 or sabitstok > 0) and kampanyalimi = 1 ");
            mysqli_stmt_bind_param($call, 'i', $deposahip);
            (mysqli_stmt_execute($call));
            $result = mysqli_stmt_get_result($call);
            if ($result) {
                $output = mysqli_fetch_row($result);
                if ($output[0] > 0) {
                    //    error_log($output[0],0);
                    $first_object = true;
                    $obj_org = clone  $obj;
                    $obj_org->id = "1000";
                    $obj_org->resim = "1000";
                    $obj_org->imageupdate = "3";
                    $obj_org->ad = "Haftanın Fırsatları";
                    $altgrup = array();
                    $altgrup["id"] = "1000";
                    $altgrup["ad"] = "İndirimli Ürünler";
                    $altgrup["grup"] = "1000";
                    $altgrup["kdvoran"] = 0;
                    $altgrup["sirala"] = 1;
                    $altgrup["aktifmi"] = 1;
                    $altgrup["deposahip"] = $deposahip;
                    $altgrup["indexid"] = "1000";
                    $altgrup_arr[] = $altgrup;
                    // Haftanın fırsatları grup objesinin altgruplar property'sine altgrup verisi ekleniyor
                    $obj_org->altgruplar = $altgrup_arr; // altgruplar yerine gruplar olarak değiştirildi
                    $arr[] = $obj_org;
                }
            } else {
                $first_object = true;
                error_log(mysqli_error($link), 0);
            }
        }
        $obj->altgruplar = isset($altgruplar[$obj->id]) ? $altgruplar[$obj->id] : [];
        $arr[] = $obj;
    }
    $ar2 = array("gruplar" => $arr);
    echo json_encode($ar2);
    //  array_push($last_ar, $ar2);
}
function geri_bildirimler($user, $uid, $baslik, $icerik, $adsoyad, $email, $telefon)
{
    global $link;
    $date = date("Y-m-d H:i:s");
    $last_ar = array();
    $arr = array();
    $call = mysqli_prepare($link, 'select id from musteribilgi where tel=? and uid=?');
    mysqli_stmt_bind_param($call, 'ss', $user, $uid);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);
    $row_count = mysqli_num_rows($rs);
    if ($row_count < 1) {
        // return;
        echo "";
    } else {
        $row = mysqli_fetch_row($rs);
    }
    $call = mysqli_prepare($link, 'insert into geri_bildirim (musteri, adsoyad, email, telefon, baslik, icerik, tarih) values (?,?,?,?,?,?,?)');
    mysqli_stmt_bind_param($call, 'issssss', $row[0], $adsoyad, $email, $telefon, $baslik, $icerik, $date);
    (mysqli_stmt_execute($call));
    $call = mysqli_prepare($link, 'SELECT * FROM geri_bildirim');
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);
    if ($rs) {
        while ($obj = mysqli_fetch_object($rs)) {
            $arr[] = $obj;
        }
        $ar2 = array("adresler" => $arr);
        array_push($last_ar, $ar2);
        $verify_status = array("status" => "success");
    } else {
        $verify_status = array("status" => "fail");
    }
    echo json_encode($verify_status);
}
function get_subeler_data($deposahip)
{
    global $link;
    //------------------ VITRIN GRUP  --------------------
    $arr = array();
    $last_ar = array();
    $rs = mysqli_query($link, "select * from depolar where deposahip=$deposahip order by gorunen_ad asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $ar2 = array("depolar" => $arr);
    echo json_encode($ar2);
}
function get_kampanyalar_data($deposahip, $depo)
{
    $deposahip = 1;
    $depo = 1;
    global $link;
    $arr = array();
    $last_ar = array();
    $rs = mysqli_query($link, "select * from kampanyalist where completed = 0 and iptalmi = 0 and aktifmi = 1  order by id asc");
    while ($obj = mysqli_fetch_assoc($rs)) {
        $kampanyalar[] = $obj;
    }
    for ($i = 0; $i < sizeof($kampanyalar); $i++) {
        $kampanya_id = $kampanyalar[$i]["id"];
        $arr = array();
        $rs = mysqli_query($link, "select stok.id,stok.ad,stok.slug,aciklama,stok.grup,stok.altgrup,ROUND (satisfiyat, 2) as satisfiyat,
        least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, stok.kdvoran,stok.imageupdate,
        kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
        birim, grup.ad as grupad, altgrup.ad as altgrupad, (select ifnull(minimumadet, 0)) as mincount from stok
        left join grup on stok.grup = grup.id and stok.deposahip = grup.deposahip
        left join altgrup on stok.altgrup = altgrup.id and stok.grup = altgrup.grup and stok.deposahip = altgrup.deposahip
        where barkod in (
           select  barkod from kampanyalistdetay where kampanyano = $kampanya_id order by id asc
           )
            and stok.deposahip = $deposahip
            and stok.depo = $depo
            and adet > 3
            and stok.grup != -1 and stok.altgrup != -1 and stok.flagweb = 1
            order by  stok.ad
        ");
        while ($obj = mysqli_fetch_object($rs)) {
            $arr[] = $obj;
        }
        $kampanyalar[$i]["kampanyadetay"] = $arr;
    }
    echo json_encode($kampanyalar);
}
function get_cart($userId)
{
    global $link;
    $sql = "
        SELECT
            sepet.id AS sepet_id,
            sepet.stok_id,
            sepet.adet AS sepet_adet,
            sepet.fiyat AS sepet_fiyat,
            stok.ad AS stok_ad,
            stok.slug AS slug,
            stok.aciklama AS stok_aciklama,
            stok.grup AS stok_grup,
            stok.altgrup AS stok_altgrup,
            ROUND(stok.satisfiyat, 2) AS stok_satisfiyat,
            LEAST(
                GREATEST(
                    LEAST(stok.adet, IFNULL(stok.siparislimit, 1000)),
                    stok.sabitstok
                ),
                IFNULL(stok.siparislimit, 1000)
            ) AS stok_adet,
            stok.kdvoran,
            stok.imageupdate,
            stok.kampanyasizsatisfiyat,
            stok.kampanyalimi,
            stok.barkod,
            stok.stok_kodu AS stok_kodu,
            stok.birim,
            IFNULL(stok.minimumadet, 0) AS stok_minimumadet
        FROM
            musteri_sepet sepet
        LEFT JOIN
            stok ON sepet.stok_id = stok.id
        WHERE
            sepet.musteri_id = ? AND
            stok.aktifmi = 1 AND
            stok.flagweb = 1 AND
            (stok.adet > 2 OR stok.sabitstok > 0) AND
            stok.satisfiyat > 0 AND
            stok.barkod != '' AND
            stok.ad != '' AND
            stok.flagweb = 1
    ";
    // Veritabanı sorgusunu çalıştır
    $stmt = mysqli_prepare($link, $sql);
    mysqli_stmt_bind_param($stmt, "i", $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    // Sepet verisi var mı kontrolü
    if (mysqli_num_rows($result) > 0) {
        $cartItems = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $cartItems[] = [
                'id' => $row['sepet_id'], // CartItem ID
                'quantity' => $row['sepet_adet'], // Quantity
                'product' => [
                    'id' => (string)$row['stok_id'], // Product ID
                    'ad' => $row['stok_ad'], // Product Name
                    'aciklama' => $row['stok_aciklama'], // Description
                    'grup' => $row['stok_grup'], // Group
                    'altgrup' => $row['stok_altgrup'], // Subgroup
                    'satisfiyat' => (string)$row['stok_satisfiyat'], // Price
                    'adet' => (string)$row['stok_adet'], // Stock Quantity
                    'kdvoran' => (string)$row['kdvoran'], // VAT rate
                    'imageupdate' => (string)$row['imageupdate'], // Image Update Date
                    'kampanyasizsatisfiyat' => (string)$row['kampanyasizsatisfiyat'], // Price without campaign
                    'kampanyalimi' => (string)$row['kampanyalimi'], // Is Campaign Active
                    'barkod' => (string)$row['barkod'], // Barcode
                    'stokkod' => (string)$row['stok_kodu'], // Stock Code
                    'birim' => (string)$row['birim'], // Unit
                    'mincount' => (string)$row['stok_minimumadet'] // Minimum Quantity
                ]
            ];
        }
        echo json_encode([
            'success' => true,
            'output' => $cartItems
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'errormsg' => 'Sepetinizde ürün bulunmamaktadır.'
        ]);
    }
}
function update_cart($userId, $productId, $quantity, $price)
{
    global $link;
    $productId = (int)$productId;
    $quantity = (int)$quantity;
    $price = (float)$price;  // Fiyatı float türüne dönüştür
    if ($quantity <= 0) {
        // Ürünü sepetteki gibi silme
        $sql = "DELETE FROM musteri_sepet WHERE musteri_id = ? AND stok_id = ?";
        $stmt = mysqli_prepare($link, $sql);
        if ($stmt === false) {
            echo json_encode(['success' => false, 'message' => 'SQL preparation failed: ' . mysqli_error($link)]);
            return;
        }
        mysqli_stmt_bind_param($stmt, "ii", $userId, $productId);
        if (mysqli_stmt_execute($stmt)) {
            get_cart($userId);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to remove product from cart.'
            ]);
        }
        return;
    }
    // Ürün sepette mevcut mu kontrol et
    $sql = "SELECT * FROM musteri_sepet WHERE musteri_id = ? AND stok_id = ?";
    $stmt = mysqli_prepare($link, $sql);
    if ($stmt === false) {
        echo json_encode(['success' => false, 'message' => 'SQL preparation failed: ' . mysqli_error($link)]);
        return;
    }
    mysqli_stmt_bind_param($stmt, "ii", $userId, $productId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    if (mysqli_num_rows($result) > 0) {
        // Ürün sepette varsa, adeti ve fiyatı güncelle
        $sql = "UPDATE musteri_sepet SET adet = ?, fiyat = ? WHERE musteri_id = ? AND stok_id = ?";
        $stmt = mysqli_prepare($link, $sql);
        if ($stmt === false) {
            echo json_encode(['success' => false, 'message' => 'SQL preparation failed: ' . mysqli_error($link)]);
            return;
        }
        // Parametreleri doğru sırayla bağladığınızdan emin olun
        mysqli_stmt_bind_param($stmt, "idii", $quantity, $price,  $userId, $productId);  // 'd' float için, 'i' integer için
        if (mysqli_stmt_execute($stmt)) {
            get_cart($userId);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to update product quantity and price.'
            ]);
        }
    } else {
        // Ürün sepette yoksa, yeni ürün ekle
        $sql = "INSERT INTO musteri_sepet (musteri_id, stok_id, adet, fiyat) VALUES (?, ?, ?, ?)";
        $stmt = mysqli_prepare($link, $sql);
        if ($stmt === false) {
            echo json_encode(['success' => false, 'message' => 'SQL preparation failed: ' . mysqli_error($link)]);
            return;
        }
        // Parametre sırasının doğru olduğundan emin olun
        mysqli_stmt_bind_param($stmt, "iiid", $userId, $productId, $quantity, $price);  // 'd' fiyat için
        if (mysqli_stmt_execute($stmt)) {
            get_cart($userId);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to add product to cart.'
            ]);
        }
    }
}
function get_vitrin_data($deposahip, $depo)
{
    global $link;
    $last_ar = array();
    if ($depo == 31) {
        $depo = 1;
    }
    //------------------ VITRIN GRUP  --------------------
    $arr = array();
    $rs = mysqli_query($link, "select * from vitringrup order by id asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["vitringrup"] = $arr;
    //------------------ VITRIN GRUP  --------------------
    // Get Vitrindetay data
    $arr = array();
    $rs = mysqli_query($link, "select stok.id,stok.ad,stok.slug,aciklama,stok.grup,stok.altgrup,ROUND (satisfiyat, 2) as satisfiyat,
     least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, stok.kdvoran,stok.imageupdate,
     kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
     birim, vitrindetay.vitringrup, grup.ad as grupad, altgrup.ad as altgrupad, (select ifnull(minimumadet, 0)) as mincount from stok
     left join vitrindetay on stok.stok_kodu = vitrindetay.urun and vitrindetay.deposahip = $deposahip
     left join grup on stok.grup = grup.id and stok.deposahip = grup.deposahip
     left join altgrup on stok.altgrup = altgrup.id and stok.grup = altgrup.grup and stok.deposahip = altgrup.deposahip
     where stok_kodu in (
        select urun from vitrindetay where deposahip = $deposahip order by id asc
        )
         and stok.deposahip = $deposahip
         and stok.depo = $depo
         and adet > 3
         and (stok.grup != -1 and stok.altgrup != -1 and grup.ad is not null and altgrup.ad is not null)
         order by vitrindetay.vitringrup, stok.ad
     ");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["vitrindetay"] = $arr;
    /* ============================================================================================== */
    /*                                            vitrinengruplar                                          */
    /* ============================================================================================== */
    $arr = array();
    $rs = mysqli_query($link, "select * from vitrinengruplar where deposahip = $deposahip order by id asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $vitrinengruplar_with_details = array();
    $index = 1;
    foreach ($arr as $vitrinengrup) {
        $engrup_arr = array();
        if ($index == 1)
            $engrup_arr = get_engrup_details($deposahip, $depo, $index);
        if ($index == 2)
            $engrup_arr = get_engrup_details($deposahip, $depo, $index);
        if ($index == 3)
            $engrup_arr = get_engrup_details($deposahip, $depo, $index);
        $vitrinengruplar_with_details[] = array_merge((array)$vitrinengrup, array("details" => $engrup_arr));
        $index++;
    }
    $last_ar["vitrinengruplar"] = $vitrinengruplar_with_details;
    echo json_encode($last_ar);
}
function get_engrup_details($deposahip, $depo, $engrup_no)
{
    global $link;
    $arr = array();
    $rs = mysqli_query($link, "
     select stok.id,stok.ad,stok.slug,aciklama,stok.grup,stok.altgrup,ROUND (satisfiyat, 2) as satisfiyat,
     least (greatest( least(adet, ifnull(siparislimit, 1000)) , sabitstok), ifnull(siparislimit, 1000) )  as adet, stok.kdvoran,stok.imageupdate,
     kampanyasizsatisfiyat,kampanyalimi,barkod,stok_kodu as stokkod,
     birim, vitrinengrup$engrup_no.vitringrup, grup.ad as grupad, altgrup.ad as altgrupad, (select ifnull(minimumadet, 0)) as mincount from stok
     left join vitrinengrup$engrup_no on stok.stok_kodu = vitrinengrup$engrup_no.urun and vitrinengrup$engrup_no.deposahip = $deposahip
     left join grup on stok.grup = grup.id and stok.deposahip = grup.deposahip
     left join altgrup on stok.altgrup = altgrup.id and stok.grup = altgrup.grup and stok.deposahip = altgrup.deposahip
     where stok_kodu in (
        select urun from vitrinengrup$engrup_no where deposahip = $deposahip order by id asc
        )
         and stok.deposahip = $deposahip
         and stok.depo = $depo
         and adet > 3
         and (stok.grup != -1 and stok.altgrup != -1 and grup.ad is not null and altgrup.ad is not null )
         order by stok.id asc
     ");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    return $arr;
}
function web_add_new_user($ceptel, $adsoyad, $email, $sifre, $os, $fbid)
{
    $date = date("Y-m-d H:i:s");
    $sifre = decrypt_web_aes($sifre);
    $password = "zdc.com.tr";
    $cryptor = new \RNCryptor\RNCryptor\Encryptor;
    $sifre = $cryptor->encrypt($sifre, $password);
    //şifreyi decrypt etmek için (ileride lazım olabilir diye )
    $sifre = str_replace(' ', '+', $sifre);
    $password = "zdc.com.tr";
    $cryptor =  new \RNCryptor\RNCryptor\Decryptor;
    $sifre_dec = $cryptor->decrypt($sifre, $password);
    global $link;
    $uid_token = md5(uniqid(mt_rand(), true));
    $verification_code = rand(100000, 999999);
    // $verification_code = 111111;
    if (!isset($_POST["os"])) {
        $os = "android";
    }
    if (!isset($_POST["fb"])) {
        $fbid = "0";
    }
    //APPLE SIGN IN
    //Burada istisna bir durum var
    //user bilgileri sadece ilk login de alınabildiginden kullanıcı register olmasa da o degerleri dbye yazmak gerekiyor
    // o yuzden dummy degerler ile musteribilgi tablosuna insert into yapıyoruz.
    //apple signin olabilir o yuzden kontrol edelim, çünkü apple sigin in ile o user bilgilerini nceden insert ediyoruz
    //buradan sonrası apple id login için yani
    $call = mysqli_prepare($link, 'select appleid,tel,isactivated from musteribilgi where appleid = ?');
    mysqli_stmt_bind_param($call, 's', $fbid);
    $rs = (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);
    $row = mysqli_fetch_assoc($rs);
    //evet apple login demektir, silip yeniden insert edeelim.
    // fbid -1 ve 0 olmamalı ve uzunlugu da en az 10 olmalı yoksa tum kullanıcılara silecek hamleler yapılabilir.
    if (strlen($fbid) > 30 &&  $fbid != "-1" && $fbid != "0" && $row['appleid'] != "-1" && $row['isactivated'] == 0 && strlen($row['tel']) == 0) {
        $call = mysqli_prepare($link, 'delete from musteribilgi where appleid = ?');
        mysqli_stmt_bind_param($call, 's', $fbid);
        $rs = (mysqli_stmt_execute($call));
        $ip = getRealIpAddr();
        $call = mysqli_prepare($link, 'insert into musteribilgi (adsoyad, tel, mail, sifre, uid, register_date, verification_code, verification_code_time, os, appleid, ipaddress ) values (?,?,?,?,?,?,?,?, ?, ?,?)');
        mysqli_stmt_bind_param($call, 'sssssssssss', $adsoyad, $ceptel, $email, $sifre, $uid_token, $date, $verification_code, $date, $os, $fbid, $ip);
        $rs = (mysqli_stmt_execute($call));
        $login_status = array("status" => "success", "uid" => $uid_token);
        //   $login_status = array( "status" => "success", "uid" => $uid_token, "cleartext_password" => $sifre_dec);
        $raw_ceptel = preg_replace("/[^0-9]/", "", $ceptel);
        SMSGonder($raw_ceptel, "Tek Kullanımlık Üyelik Doğrulama Şifreniz : " . $verification_code . " Lütfen Şifrenizi Kimse İle Paylaşmayınız.");
        //buraya kadar apple login için.
        echo json_encode($login_status);
        return;
    }
    $ip = getRealIpAddr();
    $dtarih = NULL;
    if (isset($_POST["dtarih"]) && $_POST["dtarih"] !=  "") {
        $time = strtotime($_POST["dtarih"]);
        $newformat = date('Y-m-d', $time);
        if (isset($newformat)) {
            $dtarih = $newformat;
        }
    }
    $cinsiyet = 0;
    if (isset($_POST["cinsiyet"])) {
        $cinsiyet = $_POST["cinsiyet"];
    }
    $call = mysqli_prepare($link, 'insert into musteribilgi (adsoyad, tel, mail, sifre, uid, register_date, verification_code, verification_code_time, os, fbid, kvkk, ipaddress, dtarih, cinsiyet ) values (?,?,?,?,?,?,?,?, ?,?,?,?,?,?)');
    mysqli_stmt_bind_param($call, 'ssssssssssissi', $adsoyad, $ceptel, $email, $sifre, $uid_token, $date, $verification_code, $date, $os, $fbid, $kvkk, $ip, $dtarih, $cinsiyet);
    $rs = (mysqli_stmt_execute($call));
    if ($rs) {
        $login_status = array("status" => "success", "uid" => $uid_token);
        //   $login_status = array( "status" => "success", "uid" => $uid_token, "cleartext_password" => $sifre_dec);
        //netgsm iys tablosuna yeni kayıt olan musteri bilgilerini ekleyelim
        $call = mysqli_prepare($link, 'insert into netgsm_iys (musterino, tel, created, durum, create_date ) values ((select id from musteribilgi where tel=?),?,0,1,now())');
        mysqli_stmt_bind_param($call, 'ss', $ceptel, $ceptel);
        $rs = (mysqli_stmt_execute($call));
        $raw_ceptel = preg_replace("/[^0-9]/", "", $ceptel);
        SMSGonder($raw_ceptel, "Tek Kullanımlık Üyelik Doğrulama Şifreniz : " . $verification_code . " Lütfen Şifrenizi Kimse İle Paylaşmayınız.");
    } else {
        //    echo "Error details: " . mysqli_error($link) . ".";
        $error_message = mysqli_error($link);
        if (strpos($error_message, 'Duplicate') !== false) {
            $login_status = array("status" => "duplicate", "uid" => "fail", "data" =>  mysqli_error($link));
        } else {
            $login_status = array("status" => "fail", "uid" => "fail", "data" =>  mysqli_error($link));
        }
    }
    echo json_encode($login_status);
}
function web_login_app_user($user, $password)
{
    $password = decrypt_web_aes($password);
    $sifre_dec = $password;
    //  echo $user;
    //  echo $password;
    $date = date("Y-m-d H:i:s");
    global $link;
    $call = mysqli_prepare($link, 'select adsoyad,mail,uid,isactivated,sifre,fbid from musteribilgi where tel=?');
    mysqli_stmt_bind_param($call, 's', $user);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);
    if ($rs) {
        $row = mysqli_fetch_row($rs);
        $row_count = mysqli_num_rows($rs);
        if ($row_count == 1) {
            if ($row[5] == "2") {
                if ($row[3] == 0) {
                    //eger user marketyodan geldiyse her sifre kabul sayılsın
                    $sifre_dec_db = "1";
                    $sifre_dec = "1";
                } else {
                    //dogrulanmıs marketyo kullanıcısı ise login olamasın
                    //sifremi unuttum yapıp sifresini değişmek zorunda kalsın
                    $sifre_dec_db = "0";
                    $sifre_dec = "1";
                }
            } else {
                //dbdeki şifreyi decrypt etmek için
                $password_db = $row[4];
                $code = "zdc.com.tr";
                $cryptor =  new \RNCryptor\RNCryptor\Decryptor;
                $sifre_dec_db = $cryptor->decrypt($password_db, $code);
            }
            //    echo $sifre_dec;
            //    echo $sifre_dec_db;
            if (strcmp($sifre_dec, $sifre_dec_db) == 0) {
                if ($row[3] == 1) {
                    $verify_status = array("status" => "success", "adsoyad" => $row[0], "mail" => $row[1], "uid" => $row[2]);
                } else {
                    $verify_status = array("status" => "needsverification", "adsoyad" => $row[0], "mail" => $row[1], "uid" => $row[2]);
                    resend_verification_code_forgot($user, true);
                }
            } else {
                $verify_status = array("status" => "fail");
            }
        } else {
            $verify_status = array("status" => "fail");
        }
    } else {
        $verify_status = array("status" => "fail");
    }
    echo json_encode($verify_status);
}
function web_change_password($ceptel, $expassword, $newpassword, $uid, $forgotpass = 0)
{
    $date = date("Y-m-d H:i:s");
    $expassword = decrypt_web_aes($expassword);
    $newpassword = decrypt_web_aes($newpassword);
    $password = "zdc.com.tr";
    $cryptor = new \RNCryptor\RNCryptor\Encryptor;
    $expassword = $cryptor->encrypt($expassword, $password);
    $newpassword = $cryptor->encrypt($newpassword, $password);
    $expassword = str_replace(' ', '+', $expassword);
    $newpassword = str_replace(' ', '+', $newpassword);
    //gelen şifreyi decrypt etmek için (ileride lazım olabilir diye )
    $code = "zdc.com.tr";
    $cryptor =  new \RNCryptor\RNCryptor\Decryptor;
    $sifre_dec = $cryptor->decrypt($expassword, $code);
    global $link;
    //sifreyi degisirken
    //fbid yi 2 den 0 a donusturelim ki bizinm sifreleme yontemimizi kullanacgımızı
    //bu kolondan anlıyoruz. 2 ise md5 0 ise bizim sifreleme
    $fbid = "0";
    if ($forgotpass) {
        $call = mysqli_prepare($link, 'update musteribilgi set sifre=?, password_changetime=?, isactivated=1, fbid=? where tel=? and uid=?');
        mysqli_stmt_bind_param($call, 'sssss', $newpassword, $date, $fbid, $ceptel, $uid);
        (mysqli_stmt_execute($call));
        if (mysqli_affected_rows($link) > 0) {
            $sms_status = array("status" => "success");
        } else {
            $sms_status = array("status" => "fail");
        }
    } else {
        $call = mysqli_prepare($link, 'select sifre,fbid from musteribilgi where tel=? and uid=? ');
        mysqli_stmt_bind_param($call, 'ss', $ceptel, $uid);
        (mysqli_stmt_execute($call));
        $rs = mysqli_stmt_get_result($call);
        if ($rs) {
            $row = mysqli_fetch_array($rs, MYSQLI_ASSOC);
            $sifre_db = $row['sifre'];
            $fb = $row['fbid'];
            if ($fb == "2") {
                //eger user marketyodan geldiyse her sifre kabul sayılsın
                $sifre_dec_db = "1";
                $sifre_dec = "1";
            } else {
                //dbdeki şifreyi decrypt etmek için
                $code = "zdc.com.tr";
                $cryptor =  new \RNCryptor\RNCryptor\Decryptor;
                $sifre_dec_db = $cryptor->decrypt($sifre_db, $code);
            }
            if (strcmp($sifre_dec_db, $sifre_dec) == 0) {
                $call = mysqli_prepare($link, 'update musteribilgi set sifre=?, password_changetime=?, fbid=? where tel=? and uid=?');
                mysqli_stmt_bind_param($call, 'sssss', $newpassword, $date, $fbid, $ceptel, $uid);
                (mysqli_stmt_execute($call));
                if (mysqli_affected_rows($link) > 0) {
                    $sms_status = array("status" => "success");
                } else {
                    $sms_status = array("status" => "fail");
                }
            } else {
                $sms_status = array("status" => "fail");
            }
        } else {
            $sms_status = array("status" => "fail");
        }
    }
    echo json_encode($sms_status);
}
function web_get_init_data($user, $uid, $konum, $os, $version, $fbid, $teslimattip)
{
    //merhaba ben hakan. //hakan2
    global $link;
    // error_log("konum : ".$konum,0);
    $date = date("Y-m-d H:i:s");
    //$str="insert into users (username,password,ip,country,tarih) values ('$username' , '$password','$ip', '$country_code', '$date')";
    //echo $str;
    //return;
    // error_log("test : ".$konum,0);
    $depo_no = find_branch($konum, $teslimattip);
    $depo_koordinat = "";
    $deposahip = 1;
    $depotel = "";
    $real_depo = $depo_no;
    if ($depo_no == 31) {
        $depo_no = 1;
    }
    if (strcmp($depo_no, "0") != 0) {
        $call = mysqli_prepare($link, 'select * from depolar where id=? ');
        mysqli_stmt_bind_param($call, 'i', $depo_no);
        (mysqli_stmt_execute($call));
        $rs = mysqli_stmt_get_result($call);
        if ($rs) {
            $row = mysqli_fetch_row($rs);
            $row_count = mysqli_num_rows($rs);
            $depo_koordinat = $row[2];
            $deposahip = $row[4];
        }
    }
    if (!isset($_POST["os"])) {
        $os = "web";
    }
    if (!isset($_POST["version"])) {
        $version = "-";
    }
    if (!isset($_POST["fbid"])) {
        $fbid = "0";
    }
    $arr = array();
    $last_ar = array();
    //------------------GET GROUPS --------------------
    $rs = mysqli_query($link, "select * from grup where aktifmi = 1 and deposahip = $deposahip order by sirala asc");
    $first_object = false;
    while ($obj = mysqli_fetch_object($rs)) {
        if (!$first_object) {
            //eger indirimli urun hiç yoksa o grubu eklemeyelim
            $call = mysqli_prepare($link, "select count(id) from stok where deposahip = ? and aktifmi=1 and flagweb = 1 and  (adet > 2 or sabitstok > 0) and depo = ? and kampanyalimi = 1 ");
            mysqli_stmt_bind_param($call, 'ii', $deposahip, $depo_no);
            (mysqli_stmt_execute($call));
            $result = mysqli_stmt_get_result($call);
            if ($result) {
                $output = mysqli_fetch_row($result);
                if ($output[0] > 0) {
                    //    error_log($output[0],0);
                    $first_object = true;
                    $obj_org = clone  $obj;
                    $obj_org->id = "1000";
                    $obj_org->resim = "1000";
                    $obj_org->imageupdate = "3";
                    $obj_org->ad = "Haftanın Fırsatları";
                    $arr[] = $obj_org;
                }
            } else {
                $first_object = true;
                error_log(mysqli_error($link), 0);
            }
        }
        $arr[] = $obj;
    }
    $last_ar["gruplar"] = $arr;
    /*
    //------------------ALT GRUP  --------------------
    $arr = array();
    //$rs = mysqli_query($link, "select * from altgrup order by sirala asc");
    //boş altgruplar gorunmesin diye yaptık
    $rs = mysqli_query($link, "select * from altgrup where id in (select altgrup from stok where (adet > 2 or sabitstok > 0) and aktifmi = 1 and deposahip = $deposahip) and deposahip = $deposahip order by ad asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $ar2 = array("altgruplar" => $arr);
    array_push($last_ar, $ar2);
    */
    //-----------DEPO BİLGİ-------------
    if ($depo_no == 0) {
        $depolar = array("depo_no" => $depo_no, "depo_sahip" => -1);
    } else {
        $depolar = array("depo_no" => $depo_no, "depo_koordinat" => $depo_koordinat, "depo_sahip" => $deposahip);
    }
    $last_ar["depo_bilgi"] = $depolar;
    //------------------BANNERLAR  --------------------
    $arr = array();
    $deposahip_banner = 1;
    if ($deposahip == 2)
        $deposahip_banner = 2;
    $query = "select * from banners where iptalmi = 0 and aktifmi = 1  and deposahip = $deposahip_banner and platform=1 order by siralama desc";
    //androidteki yapıda map slider içinde olmadıgı için ona ilk eleman olan boş resimli kaydı göndermemek için eklendi.
    if (strcmp($os, "web") == 0) {
        $query = "select * from banners where iptalmi = 0  and aktifmi = 1 and deposahip = $deposahip_banner and siralama != 1000000 and platform=1 order by siralama desc";
    }
    $rs = mysqli_query($link, $query);
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["banners"] = $arr;
    //------------------DINAMIK PARAMETRELER  --------------------
    $arr = array();
    $rs = mysqli_query($link, "SELECT * FROM `parameters` where id not in (select rule_id from parameters2 where deposahip = $deposahip)
     union
     select parameters2.rule_id as id, parameters.ad, parameters2.deger, parameters.aciklama, parameters.tip, parameters.aktifmi from parameters2
     left join parameters on parameters2.rule_id = parameters.id
     where deposahip = $deposahip order by id asc");
    while ($obj = mysqli_fetch_assoc($rs)) {
        /*
        if ( $depo_no == 4 &&  ($obj["ad"]   ==  "payment_type" ) )
        {
            $obj["deger"] = "1";
        }
        if ( $depo_no == 4 &&  ($obj["ad"]   ==  "default_payment_type" ) )
        {
            $obj["deger"] = "1";
        }
*/
        $arr[] = $obj;
    }
    $last_ar["parameters"] = $arr;
    $call = mysqli_prepare($link, 'update musteribilgi set version=?,last_login=?, login_count=login_count+1, os=? where tel=?');
    mysqli_stmt_bind_param($call, 'ssss', $version, $date, $os,  $user);
    $rs = (mysqli_stmt_execute($call));
    //randevu saatlerini ve musaitlik durumlarını çekelim
    $randevular = get_randevu_stats($deposahip, $real_depo);
    $enyakin = $randevular["enyakin"];
    $randevu_all = $randevular["randevular"];
    $ar2 = array("enyakinrandevu" => $enyakin, "randevu_bilgi" => $randevu_all);
    $last_ar["randevular"] = $ar2;
    //todo : buradaki vtirin ile alakalı alanları kaldıralım
    // Depolar arrayinin indexi değişecke bunu da duzeltmek gerekecektir.
    //------------------ VITRIN GRUP  --------------------
    $arr = array();
    $rs = mysqli_query($link, "select * from vitringrup   order by id asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["vitringrup"] = $arr;
    //------------------ VITRIN GRUP  --------------------
    $arr = array();
    $rs = mysqli_query($link, "select * from vitrindetay where deposahip = $deposahip order by id asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["vitrindetay"] = $arr;
    $arr = array();
    $rs = mysqli_query($link, "select * from vitrinengruplar where deposahip = $deposahip order by id asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["vitrinengruplar"] = $arr;
    $arr = array();
    $rs = mysqli_query($link, "select * from vitrinengrup1 where deposahip = $deposahip order by id asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["vitrinengrup1"] = $arr;
    $arr = array();
    $rs = mysqli_query($link, "select * from vitrinengrup2  where deposahip = $deposahip order by id asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["vitrinengrup2"] = $arr;
    $arr = array();
    $rs = mysqli_query($link, "select * from vitrinengrup3  where deposahip = $deposahip order by id asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["vitrinengrup3"] = $arr;
    $arr = array();
    $rs = mysqli_query($link, "select * from depolar where iptalmi=0 order by id asc");
    while ($obj = mysqli_fetch_object($rs)) {
        $arr[] = $obj;
    }
    $last_ar["depolar"] = $arr;
    echo json_encode($last_ar);
}
/* ============================================================================================== */
/*                                        Utility Functions                                       */
/* ============================================================================================== */
function validate_jwt()
{
    $headers = apache_request_headers(); // Apache'yi kullanıyorsanız
    // Veya başka bir sunucu ortamında $_SERVER['HTTP_AUTHORIZATION'] da kullanılabilir.
    error_log("header: " . json_encode($headers));
    if (isset($headers['Authorization'])) {
        $authHeader = $headers['Authorization'];
        $token = str_replace('Bearer ', '', $authHeader);
        $secret_key = "48Scw74aAgf16wvAhhr85411A18w3";
        try {
            $decoded = JWT::decode($token, new Key($secret_key, 'HS256'));
            if ($decoded->exp < time()) {
                return null;
            }
            return $decoded;
        } catch (Exception $e) {
            return null;
        }
    }
    return null;
}
function authenticate_user()
{
    $decoded = validate_jwt();
    error_log("decode: " . json_encode($decoded));
    if ($decoded === null) {
        // error_log("Token validation failed.");
        return null;
    }
    // $_SESSION['userId'] = $decoded->userId;
    return $decoded->userId;
}

/* ============================================================================================== */
/*                                             classes                                            */
/* ============================================================================================== */
class SuccessResult
{
    public $success;
    public $output;
    public function __construct($data = null)
    {
        $this->success = true;
        $this->output = $data;
    }
    public function send($code = 200)
    {
        http_response_code($code);
        echo json_encode($this, JSON_PRETTY_PRINT);
    }
}
class ErrorResult
{
    public $success;
    public $errormsg;
    public function __construct($errormsg)
    {
        $this->success = false;
        $this->errormsg = $errormsg;
    }
    public function send($code = 500)
    {
        http_response_code($code);
        echo json_encode($this, JSON_PRETTY_PRINT);
    }
}
