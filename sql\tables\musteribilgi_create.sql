-- <PERSON><PERSON><PERSON><PERSON> tablosu oluşturma SQL'i
-- <PERSON>u dosya musteribilgi tablosunun temel yapısını içerir

CREATE TABLE `musteribilgi` ( 
  `id` INT NOT NULL AUTO_INCREMENT, 
  `adsoyad` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL, 
  `tel` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL, 
  `mail` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL, 
  `sifre` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL, 
  `uid` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL, 
  `isactivated` TINYINT(1) NOT NULL DEFAULT 0, 
  `register_date` DATETIME DEFAULT NULL, 
  `verification_code` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL, 
  `verification_code_time` DATETIME DEFAULT NULL, 
  `password_changetime` DATETIME DEFAULT NULL, 
  `os` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL, 
  `version` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL, 
  `last_login` DATETIME DEFAULT NULL, 
  `login_count` INT DEFAULT 0, 
  `fbid` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0', 
  `appleid` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '-1', 
  `carikod` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0', 
  `kvkk` TINYINT DEFAULT NULL, 
  `ipaddress` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL, 
  `cinsiyet` TINYINT NOT NULL DEFAULT 0, 
  `dtarih` DATE DEFAULT NULL, 
  `pw_version` INT DEFAULT 1, 
  PRIMARY KEY (`id`), 
  KEY `idx_tel` (`tel`), 
  KEY `idx_sifre` (`sifre`), 
  KEY `idx_uid` (`uid`), 
  KEY `idx_isactivated` (`isactivated`), 
  KEY `idx_verification_code` (`verification_code`), 
  KEY `idx_last_login` (`last_login`), 
  KEY `idx_login_count` (`login_count`), 
  KEY `idx_fbid` (`fbid`) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
