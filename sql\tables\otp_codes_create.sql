-- <PERSON><PERSON> kodları tablosu oluşturma SQL'i
-- <PERSON><PERSON> tablo OTP (One Time Password) kodlarını saklamak için kullanılır

CREATE TABLE `otp_codes` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `phone_number` VARCHAR(20) NOT NULL COMMENT 'Telefon numarası (sadec<PERSON> rakam<PERSON>)',
  `otp_code` VARCHAR(8) NOT NULL COMMENT 'OTP kodu',
  `expires_at` TIMESTAMP NOT NULL COMMENT 'OTP kodunun geçerlilik süresi',
  `attempts` INT DEFAULT 0 COMMENT 'Deneme sayısı',
  `is_used` BOOLEAN DEFAULT FALSE COMMENT 'Kod kullanıldı mı?',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Oluşturulma zamanı',
  INDEX `idx_phone_used` (`phone_number`, `is_used`),
  INDEX `idx_expires_at` (`expires_at`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OTP kodları tablosu';

-- Eski OTP kodlarını temizlemek için stored procedure (opsiyonel)
DELIMITER //
CREATE PROCEDURE CleanExpiredOTPCodes()
BEGIN
    DELETE FROM otp_codes 
    WHERE expires_at < NOW() 
    OR (is_used = TRUE AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR));
END //
DELIMITER ;

-- Otomatik temizlik için event scheduler (opsiyonel - MySQL event scheduler aktif olmalı)
-- CREATE EVENT IF NOT EXISTS clean_otp_codes
-- ON SCHEDULE EVERY 1 HOUR
-- DO CALL CleanExpiredOTPCodes();
